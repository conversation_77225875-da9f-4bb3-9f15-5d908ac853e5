/**
 * 查看项目页面JavaScript
 */

let currentProject = null;

/**
 * 初始化查看项目页面
 */
function initViewProjectPage() {
    console.log('初始化查看项目页面');

    // 从URL参数获取项目ID
    const urlParams = new URLSearchParams(window.location.hash.split('?')[1] || '');
    const projectId = urlParams.get('id');

    if (!projectId) {
        showMessage('缺少项目ID参数', 'error');
        router.navigateTo('projects');
        return;
    }

    loadProjectDetails(projectId);
}

/**
 * 加载项目详情
 */
async function loadProjectDetails(projectId) {
    try {
        showProjectLoading(true);

        const response = await API.projects.getById(projectId);
        if (!response.success) {
            showMessage('获取项目信息失败', 'error');
            router.navigateTo('projects');
            return;
        }

        currentProject = response.data;
        renderProjectDetails(currentProject);

        // 加载所有相关数据
        loadProjectMembers();
        loadProjectTasks();
        loadProjectBugs();

    } catch (error) {
        console.error('Failed to load project details:', error);
        showMessage('加载项目信息失败', 'error');
        router.navigateTo('projects');
    } finally {
        showProjectLoading(false);
    }
}

/**
 * 渲染项目详情
 */
function renderProjectDetails(project) {
    // 更新页面标题
    document.getElementById('projectTitle').textContent = project.name;

    // 更新徽章
    const badgesContainer = document.getElementById('projectBadges');
    badgesContainer.innerHTML = `
        <div class="project-badge">${getStatusText(project.status)}</div>
        <div class="project-badge">${getPriorityText(project.priority)}</div>
    `;

    // 更新基本信息
    document.getElementById('projectDescription').textContent = project.description || '暂无描述';
    document.getElementById('projectManager').textContent = project.manager?.fullName || '未分配';
    document.getElementById('projectStartDate').textContent = project.startDate ? formatDate(project.startDate) : '未设置';
    document.getElementById('projectEndDate').textContent = project.endDate ? formatDate(project.endDate) : '未设置';
    document.getElementById('projectCreatedAt').textContent = formatDate(project.createdAt);
    document.getElementById('projectUpdatedAt').textContent = formatDate(project.updatedAt);


}

/**
 * 显示项目加载状态
 */
function showProjectLoading(show) {
    const titleElement = document.getElementById('projectTitle');
    const badgesElement = document.getElementById('projectBadges');

    if (show) {
        titleElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载中...';
        badgesElement.innerHTML = '';
    }
}

/**
 * 加载项目成员
 */
async function loadProjectMembers() {
    if (!currentProject) return;

    const membersContainer = document.getElementById('projectMembersDisplay');
    if (!membersContainer) return;

    try {
        membersContainer.innerHTML = '<div class="loading-text"><i class="fas fa-spinner fa-spin"></i> 加载成员列表...</div>';

        const response = await API.projects.getMembers(currentProject.id);
        const members = response.data || [];

        if (members.length === 0) {
            membersContainer.innerHTML = '<div class="empty-text">暂无项目成员</div>';
            return;
        }

        const html = `
            <div class="members-display">
                ${members.map(member => `
                    <div class="member-tag">
                        <span class="member-name">${escapeHtml(member.user?.fullName || '未知用户')}</span>
                        <span class="member-role">${getProjectMemberRoleDisplayName(member.role)}</span>
                    </div>
                `).join('')}
            </div>
        `;

        membersContainer.innerHTML = html;

    } catch (error) {
        console.error('Failed to load project members:', error);
        membersContainer.innerHTML = '<div class="error-text">加载成员失败</div>';
    }
}



/**
 * 加载项目任务
 */
async function loadProjectTasks() {
    const tasksContainer = document.getElementById('projectTasksDisplay');

    if (!currentProject) return;

    try {
        tasksContainer.innerHTML = '<div class="loading-text"><i class="fas fa-spinner fa-spin"></i> 加载任务列表...</div>';

        const response = await API.tasks.getByProjectId(currentProject.id);
        const tasks = response.data || [];

        if (tasks.length === 0) {
            tasksContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-tasks"></i>
                    <h3>暂无任务</h3>
                    <p>该项目还没有任务</p>
                </div>
            `;
            return;
        }

        const html = `
            <div class="tasks-table">
                <div class="table-header">
                    <div class="header-col task-id-col">ID</div>
                    <div class="header-col task-title-col">任务标题</div>
                    <div class="header-col task-status-col">状态</div>
                    <div class="header-col task-priority-col">优先级</div>
                    <div class="header-col task-assignee-col">负责人</div>
                    <div class="header-col task-due-date-col">截止日期</div>
                    <div class="header-col task-actions-col">操作</div>
                </div>
                ${tasks.map(task => renderProjectTaskRow(task)).join('')}
            </div>
        `;

        tasksContainer.innerHTML = html;

    } catch (error) {
        console.error('Failed to load project tasks:', error);
        tabContent.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>加载失败</h3>
                <p>无法加载任务列表</p>
                <button class="btn btn-primary" onclick="loadProjectTasks()">重试</button>
            </div>
        `;
    }
}

/**
 * 加载项目Bug
 */
async function loadProjectBugs() {
    const bugsContainer = document.getElementById('projectBugsDisplay');

    if (!currentProject) return;

    try {
        bugsContainer.innerHTML = '<div class="loading-text"><i class="fas fa-spinner fa-spin"></i> 加载Bug列表...</div>';

        const response = await API.bugs.getByProjectId(currentProject.id);
        const bugs = response.data || [];

        if (bugs.length === 0) {
            bugsContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-bug"></i>
                    <h3>暂无Bug</h3>
                    <p>该项目还没有Bug记录</p>
                </div>
            `;
            return;
        }

        const html = `
            <div class="bugs-table">
                <div class="table-header">
                    <div class="header-col bug-id-col">ID</div>
                    <div class="header-col bug-title-col">Bug标题</div>
                    <div class="header-col bug-status-col">状态</div>
                    <div class="header-col bug-priority-col">优先级</div>
                    <div class="header-col bug-severity-col">严重程度</div>
                    <div class="header-col bug-assignee-col">负责人</div>
                    <div class="header-col bug-type-col">类型</div>
                    <div class="header-col bug-actions-col">操作</div>
                </div>
                ${bugs.map(bug => renderProjectBugRow(bug)).join('')}
            </div>
        `;

        bugsContainer.innerHTML = html;

    } catch (error) {
        console.error('Failed to load project bugs:', error);
        tabContent.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>加载失败</h3>
                <p>无法加载Bug列表</p>
                <button class="btn btn-primary" onclick="loadProjectBugs()">重试</button>
            </div>
        `;
    }
}



/**
 * 编辑当前项目
 */
function editCurrentProject() {
    if (currentProject) {
        router.navigateTo('edit-project', { id: currentProject.id });
    }
}



/**
 * 查看任务
 */
function viewTask(taskId) {
    router.navigateTo('view-task', { id: taskId });
}

/**
 * 编辑任务
 */
function editTask(taskId) {
    router.navigateTo('edit-task', { id: taskId });
}

/**
 * 查看Bug
 */
function viewBug(bugId) {
    router.navigateTo('view-bug', { id: bugId });
}

/**
 * 编辑Bug
 */
function editBug(bugId) {
    router.navigateTo('edit-bug', { id: bugId });
}

/**
 * 返回项目列表
 */
function goBackToProjects() {
    router.navigateTo('projects');
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
    const statusMap = {
        'PLANNING': '规划中',
        'IN_PROGRESS': '进行中',
        'COMPLETED': '已完成',
        'ON_HOLD': '暂停',
        'CANCELLED': '已取消'
    };
    return statusMap[status] || status;
}

/**
 * 获取优先级文本
 */
function getPriorityText(priority) {
    const priorityMap = {
        'LOW': '低优先级',
        'MEDIUM': '中优先级',
        'HIGH': '高优先级',
        'CRITICAL': '紧急'
    };
    return priorityMap[priority] || priority;
}

/**
 * 渲染项目任务行
 */
function renderProjectTaskRow(task) {
    return `
        <div class="task-row" data-task-id="${task.id}">
            <div class="task-id-col">
                <span class="task-id">${task.id}</span>
            </div>
            <div class="task-title-col">
                <span class="task-title">${escapeHtml(task.title)}</span>
            </div>
            <div class="task-status-col">
                ${getStatusBadge(task.status, 'task')}
            </div>
            <div class="task-priority-col">
                ${getPriorityBadge(task.priority)}
            </div>
            <div class="task-assignee-col">
                ${task.assignee ? escapeHtml(task.assignee.fullName) : '未分配'}
            </div>
            <div class="task-due-date-col ${task.dueDate && isOverdue(task.dueDate) ? 'overdue' : ''}">
                ${task.dueDate ? formatDate(task.dueDate, 'MM-DD') : '-'}
            </div>
            <div class="task-actions-col">
                <button class="btn btn-sm btn-outline" onclick="viewTask(${task.id})" title="查看详情">
                    <i class="fas fa-eye"></i> 查看
                </button>
            </div>
        </div>
    `;
}

/**
 * 检查任务是否过期
 */
function isOverdue(dueDate) {
    if (!dueDate) return false;
    const due = new Date(dueDate);
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    return due < now;
}

/**
 * 渲染项目Bug行
 */
function renderProjectBugRow(bug) {
    return `
        <div class="bug-row" data-bug-id="${bug.id}">
            <div class="bug-id-col">
                <span class="bug-id">${bug.id}</span>
            </div>
            <div class="bug-title-col">
                <span class="bug-title">${escapeHtml(bug.title)}</span>
            </div>
            <div class="bug-status-col">
                ${getStatusBadge(bug.status, 'bug')}
            </div>
            <div class="bug-priority-col">
                ${getPriorityBadge(bug.priority)}
            </div>
            <div class="bug-severity-col">
                ${bug.severity ? getSeverityBadge(bug.severity) : '-'}
            </div>
            <div class="bug-assignee-col">
                ${bug.assignee ? escapeHtml(bug.assignee.fullName) : '未分配'}
            </div>
            <div class="bug-type-col">
                ${bug.bugType ? getBugTypeText(bug.bugType) : '-'}
            </div>
            <div class="bug-actions-col">
                <button class="btn btn-sm btn-outline" onclick="viewBug(${bug.id})" title="查看详情">
                    <i class="fas fa-eye"></i> 查看
                </button>
            </div>
        </div>
    `;
}

/**
 * 获取Bug类型文本
 */
function getBugTypeText(bugType) {
    const typeMap = {
        'FUNCTIONAL': '功能缺陷',
        'PERFORMANCE': '性能问题',
        'UI': '界面问题',
        'COMPATIBILITY': '兼容性问题',
        'SECURITY': '安全问题',
        'DATA': '数据问题',
        'OTHER': '其他'
    };
    return typeMap[bugType] || bugType;
}

/**
 * 获取严重程度徽章
 */
function getSeverityBadge(severity) {
    const severityMap = {
        'CRITICAL': { text: '严重', class: 'badge-danger' },
        'MAJOR': { text: '重要', class: 'badge-warning' },
        'MINOR': { text: '一般', class: 'badge-info' },
        'TRIVIAL': { text: '轻微', class: 'badge-secondary' }
    };

    const config = severityMap[severity] || { text: severity, class: 'badge-secondary' };
    return `<span class="badge ${config.class}">${config.text}</span>`;
}
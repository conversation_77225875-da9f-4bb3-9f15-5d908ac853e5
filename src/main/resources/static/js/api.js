// API调用模块

// 防止重复弹窗的标志
let isShowingAuthAlert = false;

const API = {
    baseURL: '/api',

    // 获取存储的Token
    getToken() {
        return localStorage.getItem('authToken');
    },

    // 设置Token
    setToken(token) {
        if (token) {
            localStorage.setItem('authToken', token);
        } else {
            localStorage.removeItem('authToken');
        }
    },

    // 清除Token
    clearToken() {
        localStorage.removeItem('authToken');
    },

    // 通用请求方法
    async request(url, options = {}) {
        const requestId = Math.random().toString(36).substr(2, 9);
        const startTime = Date.now();

        console.log(`🚀 [${requestId}] API请求开始:`, {
            url: this.baseURL + url,
            method: options.method || 'GET',
            timestamp: new Date().toISOString()
        });

        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            credentials: 'same-origin',
            ...options
        };

        // 添加JWT Token到请求头
        const token = this.getToken();
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
            console.log(`🔑 [${requestId}] 添加认证Token:`, {
                tokenLength: token.length,
                tokenPrefix: token.substring(0, 20) + '...'
            });
        } else {
            console.warn(`⚠️ [${requestId}] 未找到认证Token`);
        }

        if (config.body && typeof config.body === 'object') {
            console.log(`📤 [${requestId}] 请求体:`, config.body);
            config.body = JSON.stringify(config.body);
        }

        console.log(`📋 [${requestId}] 请求配置:`, {
            headers: config.headers,
            method: config.method || 'GET',
            hasBody: !!config.body
        });

        try {
            const response = await fetch(this.baseURL + url, config);
            const responseTime = Date.now() - startTime;

            console.log(`📥 [${requestId}] 响应接收:`, {
                status: response.status,
                statusText: response.statusText,
                responseTime: responseTime + 'ms',
                headers: Object.fromEntries(response.headers.entries())
            });

            const data = await response.json();

            console.log(`📊 [${requestId}] 响应数据:`, data);

            // 检查认证相关错误
            if (response.status === 401 || (response.status === 400 && url.includes('/auth/current'))) {
                console.warn(`🔒 [${requestId}] 认证失败:`, {
                    status: response.status,
                    data: data,
                    url: url,
                    currentPath: window.location.pathname
                });
                this.clearToken();

                // 只有在非登录页面时才跳转到登录页面，避免无限循环
                if (window.location.pathname !== '/login.html' && window.location.pathname !== '/register.html') {
                    console.log(`🔄 [${requestId}] 重定向到登录页面`);
                    // 对于401错误显示提示，400错误静默处理，并防止重复弹窗
                    if (response.status === 401 && !isShowingAuthAlert) {
                        isShowingAuthAlert = true;
                        alert('登录已过期，请重新登录');
                        // 2秒后重置标志，防止长时间锁定
                        setTimeout(() => {
                            isShowingAuthAlert = false;
                        }, 2000);
                    }
                    window.location.href = '/login.html';
                } else {
                    console.log(`🔒 [${requestId}] 在登录页面检测到认证失败，不进行重定向`);
                }

                const error = new Error(response.status === 401 ? 'Token已过期，请重新登录' : '认证失败，请重新登录');
                error.status = response.status;
                error.response = data;
                throw error;
            }

            // 检查业务逻辑是否成功（基于新的响应码系统）
            // 新的响应格式：{success: boolean, code: number, message: string, data: any}
            // 成功码：10000，错误码：20000+
            // 特殊处理：权限检查API即使返回权限不足也不应该抛出异常
            const isPermissionCheckAPI = url.includes('/permissions/check');

            if (!data.success || data.code !== 10000) {
                console.log(`⚠️ [${requestId}] 业务逻辑响应:`, {
                    code: data.code,
                    message: data.message,
                    success: data.success,
                    isPermissionCheck: isPermissionCheckAPI
                });

                // 权限检查API的权限不足响应不应该抛出异常
                if (isPermissionCheckAPI && (data.code === 20003 || data.code === 20002)) {
                    console.log(`🔒 [${requestId}] 权限检查API返回权限不足，正常返回响应`);
                    return data; // 正常返回，不抛出异常
                }

                // 其他业务逻辑错误才抛出异常
                console.error(`❌ [${requestId}] 业务逻辑错误:`, {
                    code: data.code,
                    message: data.message,
                    success: data.success,
                    data: data
                });

                const error = new Error(data.message || `Business error! code: ${data.code}`);
                error.status = response.status;
                error.code = data.code;
                error.response = data;
                throw error;
            }

            if (!response.ok) {
                console.error(`❌ [${requestId}] HTTP错误:`, {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                });
                const error = new Error(data.message || `HTTP error! status: ${response.status}`);
                error.status = response.status;
                error.code = data.code;
                error.response = data;
                throw error;
            }

            console.log(`✅ [${requestId}] 请求成功完成，耗时: ${responseTime}ms`);
            return data;

        } catch (error) {
            const responseTime = Date.now() - startTime;
            console.error(`💥 [${requestId}] API请求失败:`, {
                url: this.baseURL + url,
                method: options.method || 'GET',
                error: error.message,
                status: error.status,
                responseTime: responseTime + 'ms',
                stack: error.stack
            });
            throw error;
        }
    },
    
    // GET请求
    async get(url) {
        return this.request(url, { method: 'GET' });
    },
    
    // POST请求
    async post(url, data) {
        return this.request(url, {
            method: 'POST',
            body: data
        });
    },
    
    // PUT请求
    async put(url, data) {
        return this.request(url, {
            method: 'PUT',
            body: data
        });
    },
    
    // DELETE请求
    async delete(url) {
        return this.request(url, { method: 'DELETE' });
    },
    
    // 认证相关API
    auth: {
        // 登录
        async login(credentials) {
            return API.post('/auth/login', credentials);
        },
        
        // 注册
        async register(userData) {
            return API.post('/auth/register', userData);
        },
        
        // 登出
        async logout() {
            try {
                const result = await API.post('/auth/logout');
                // 清除本地存储的Token
                API.clearToken();
                return result;
            } catch (error) {
                // 即使登出API失败，也要清除本地Token
                API.clearToken();
                throw error;
            }
        },
        
        // 获取当前用户
        async getCurrentUser() {
            console.log('🔍 API.auth.getCurrentUser() 开始调用...');
            console.log('🔍 当前Token:', API.getToken() ? 'Token存在' : 'Token不存在');
            try {
                const result = await API.get('/auth/current');
                console.log('📋 getCurrentUser API响应:', result);
                return result;
            } catch (error) {
                console.error('❌ getCurrentUser API调用失败:', error);
                throw error;
            }
        },
        
        // 修改密码
        async changePassword(passwordData) {
            return API.post('/auth/change-password', passwordData);
        },

        // 更新个人资料
        async updateProfile(profileData) {
            return API.post('/auth/update-profile', profileData);
        },

        // 忘记密码
        async forgotPassword(email) {
            return API.post('/auth/forgot-password', { email });
        },

        // 重置密码
        async resetPassword(token, newPassword) {
            return API.post('/auth/reset-password', { token, newPassword });
        }
    },
    
    // 用户管理API
    users: {
        // 获取所有用户
        async getAll() {
            return API.get('/users');
        },
        
        // 根据ID获取用户
        async getById(id) {
            return API.get(`/users/${id}`);
        },
        
        // 根据角色获取用户
        async getByRole(role) {
            return API.get(`/users/role/${role}`);
        },
        
        // 创建用户
        async create(userData) {
            return API.post('/users', userData);
        },
        
        // 更新用户
        async update(id, userData) {
            return API.put(`/users/${id}`, userData);
        },
        
        // 删除用户
        async delete(id) {
            return API.delete(`/users/${id}`);
        },
        
        // 获取用户统计
        async getStatistics() {
            return API.get('/users/statistics');
        },

        // 获取特定用户的个人统计信息
        async getPersonalStatistics(id) {
            return API.get(`/users/${id}/statistics`);
        },

        // 获取用户最近活动
        async getUserActivities(id, limit = 10) {
            return API.get(`/users/${id}/activities?limit=${limit}`);
        },

        // 获取用户工作进度
        async getUserProgress(id) {
            return API.get(`/users/${id}/progress`);
        },

        // 重置用户密码
        async resetPassword(id, passwordData) {
            return API.post(`/users/${id}/reset-password`, passwordData);
        }
    },
    
    // 项目管理API
    projects: {
        // 获取所有项目
        async getAll() {
            return API.get('/projects');
        },
        
        // 根据ID获取项目
        async getById(id) {
            return API.get(`/projects/${id}`);
        },
        
        // 根据状态获取项目
        async getByStatus(status) {
            return API.get(`/projects/status/${status}`);
        },
        
        // 获取我的项目
        async getMy() {
            return API.get('/projects/my');
        },
        
        // 创建项目
        async create(projectData) {
            return API.post('/projects', projectData);
        },
        
        // 更新项目
        async update(id, projectData) {
            return API.put(`/projects/${id}`, projectData);
        },
        
        // 删除项目
        async delete(id) {
            return API.delete(`/projects/${id}`);
        },
        
        // 获取项目统计
        async getStatistics() {
            return API.get('/projects/statistics');
        },

        // 获取项目成员
        async getMembers(projectId) {
            return API.get(`/projects/${projectId}/members`);
        },

        // 添加项目成员
        async addMember(projectId, memberData) {
            return API.post(`/projects/${projectId}/members`, memberData);
        },

        // 批量设置项目成员
        async setMembers(projectId, memberData) {
            return API.put(`/projects/${projectId}/members`, memberData);
        },

        // 删除项目成员
        async removeMember(projectId, userId) {
            return API.delete(`/projects/${projectId}/members/${userId}`);
        }
    },
    
    // 任务管理API
    tasks: {
        // 获取所有任务
        async getAll() {
            return API.get('/tasks');
        },
        
        // 根据ID获取任务
        async getById(id) {
            return API.get(`/tasks/${id}`);
        },
        
        // 根据项目ID获取任务
        async getByProjectId(projectId) {
            return API.get(`/tasks/project/${projectId}`);
        },
        
        // 根据分配人ID获取任务
        async getByAssigneeId(assigneeId) {
            return API.get(`/tasks/assignee/${assigneeId}`);
        },
        
        // 根据状态获取任务
        async getByStatus(status) {
            return API.get(`/tasks/status/${status}`);
        },
        
        // 获取我的任务
        async getMy() {
            return API.get('/tasks/my');
        },
        
        // 创建任务
        async create(taskData) {
            return API.post('/tasks', taskData);
        },
        
        // 更新任务
        async update(id, taskData) {
            return API.put(`/tasks/${id}`, taskData);
        },
        
        // 更新任务状态
        async updateStatus(id, status) {
            return API.put(`/tasks/${id}/status`, { status });
        },
        
        // 删除任务
        async delete(id) {
            return API.delete(`/tasks/${id}`);
        },
        
        // 获取任务统计
        async getStatistics() {
            return API.get('/tasks/statistics');
        }
    },
    
    // 任务评论API
    comments: {
        // 获取任务评论
        async getByTaskId(taskId) {
            return API.get(`/tasks/${taskId}/comments`);
        },

        // 添加评论
        async create(taskId, commentData) {
            return API.post(`/tasks/${taskId}/comments`, commentData);
        },

        // 删除评论
        async delete(commentId) {
            return API.delete(`/comments/${commentId}`);
        }
    },

    // 通知相关API
    notifications: {
        // 获取我的通知列表
        async getMy(page = 1, size = 20) {
            return API.get(`/notifications?page=${page}&size=${size}`);
        },

        // 获取未读通知列表
        async getUnread() {
            return API.get('/notifications/unread');
        },

        // 获取未读通知数量
        async getUnreadCount() {
            return API.get('/notifications/unread/count');
        },

        // 标记通知为已读
        async markAsRead(id) {
            return API.put(`/notifications/${id}/read`);
        },

        // 标记所有通知为已读
        async markAllAsRead() {
            return API.put('/notifications/read-all');
        },

        // 删除通知
        async delete(id) {
            return API.delete(`/notifications/${id}`);
        },

        // 创建系统通知（管理员）
        async createSystem(notificationData) {
            return API.post('/notifications/system', notificationData);
        }
    },

    // Bug相关API
    bugs: {
        // 获取所有Bug
        async getAll() {
            return API.get('/bugs');
        },

        // 根据ID获取Bug
        async getById(id) {
            return API.get(`/bugs/${id}`);
        },

        // 根据项目ID获取Bug
        async getByProjectId(projectId) {
            return API.get(`/bugs/project/${projectId}`);
        },

        // 根据分配人ID获取Bug
        async getByAssigneeId(assigneeId) {
            return API.get(`/bugs/assignee/${assigneeId}`);
        },

        // 根据状态获取Bug
        async getByStatus(status) {
            return API.get(`/bugs/status/${status}`);
        },

        // 获取我的Bug
        async getMy() {
            return API.get('/bugs/my');
        },

        // 创建Bug
        async create(bugData) {
            return API.post('/bugs', bugData);
        },

        // 更新Bug
        async update(id, bugData) {
            return API.put(`/bugs/${id}`, bugData);
        },

        // 更新Bug状态
        async updateStatus(id, status) {
            return API.put(`/bugs/${id}/status`, { status });
        },

        // 分配Bug
        async assign(id, assigneeId) {
            return API.put(`/bugs/${id}/assign`, { assigneeId });
        },

        // 删除Bug
        async delete(id) {
            return API.delete(`/bugs/${id}`);
        },

        // 获取Bug评论
        async getComments(bugId) {
            return API.get(`/bugs/${bugId}/comments`);
        },

        // 添加Bug评论
        async createComment(bugId, commentData) {
            return API.post(`/bugs/${bugId}/comments`, commentData);
        },

        // 删除Bug评论
        async deleteComment(commentId) {
            return API.delete(`/bugs/comments/${commentId}`);
        },

        // 获取Bug统计
        async getStatistics() {
            return API.get('/bugs/statistics');
        }
    }
};

// 全局错误处理
window.addEventListener('unhandledrejection', event => {
    console.error('Unhandled promise rejection:', event.reason);

    const error = event.reason;

    // 检查是否需要重新登录
    if (typeof shouldRedirectToLogin === 'function' && shouldRedirectToLogin(error)) {
        // 避免在登录页面重复跳转
        if (!window.location.pathname.includes('login.html')) {
            window.location.href = '/login.html';
            return;
        }
    }

    // 显示用户友好的错误消息
    if (typeof showMessage === 'function' && typeof handleApiError === 'function') {
        const userMessage = handleApiError(error);
        showMessage(userMessage, 'error');
    } else if (typeof showMessage === 'function') {
        showMessage('操作失败，请稍后重试', 'error');
    }
});

// 导出API对象
window.API = API;

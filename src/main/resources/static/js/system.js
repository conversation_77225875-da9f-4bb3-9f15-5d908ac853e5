// 系统管理JavaScript

/**
 * 加载系统管理页面
 */
async function loadSystem() {
    try {
        console.log('🔍 [System] === 开始加载系统管理页面 ===');
        showSystemLoading(true);

        // 保存当前主题设置
        const isDarkMode = document.body.classList.contains('dark-mode');
        console.log(`🎨 [System] 加载系统页面前的主题模式: ${isDarkMode ? '深色' : '浅色'}`);
        console.log(`🔍 [System] body类列表:`, document.body.classList.toString());
        
        // 记录当前DOM状态
        console.log(`🔍 [System] 当前body元素:`, document.body);
        console.log(`🔍 [System] 当前主题切换按钮:`, document.getElementById('themeToggleBtn'));

        // 检查用户认证状态和权限
        if (!currentUser) {
            console.warn('⚠️ [System] 用户未认证，重定向到登录页面');
            window.location.href = '/login.html';
            return;
        }

        console.log('👤 [System] 当前用户信息:', {
            username: currentUser.username,
            role: currentUser.role,
            email: currentUser.email
        });

        // 检查管理员权限
        if (currentUser.role !== 'ADMIN') {
            console.warn('⚠️ [System] 权限不足，用户角色:', currentUser.role);
            showMessage('权限不足，只有管理员可以访问系统管理页面', 'error');
            return;
        }
        
        // 在加载系统数据前再次检查主题
        console.log(`🎨 [System] 加载系统数据前的主题模式: ${document.body.classList.contains('dark-mode') ? '深色' : '浅色'}`);
        
        // 确保主题设置不变
        if (isDarkMode && !document.body.classList.contains('dark-mode')) {
            console.log(`🔄 [System] 检测到主题被改变，恢复深色模式`);
            document.body.classList.add('dark-mode');
            
            // 更新主题图标
            const themeToggleBtn = document.getElementById('themeToggleBtn');
            if (themeToggleBtn) {
                console.log(`🔄 [System] 更新主题切换按钮图标`);
                const moonIcon = themeToggleBtn.querySelector('.fa-moon');
                const sunIcon = themeToggleBtn.querySelector('.fa-sun');
                
                if (moonIcon) moonIcon.style.display = 'none';
                if (sunIcon) sunIcon.style.display = 'inline-block';
                
                themeToggleBtn.title = '切换到浅色模式';
            }
        } else if (isDarkMode) {
            console.log(`✅ [System] 主题模式保持一致，无需恢复`);
        } else {
            console.log(`✅ [System] 当前为浅色模式，无需处理`);
        }

        console.log('开始并行请求系统状态和数据库健康信息...');

        // 并行加载系统状态和数据库健康信息
        console.log('发送API请求:', {
            systemStatus: '/system/status',
            databaseHealth: currentUser.role === 'ADMIN' ? '/system/database/health' : 'skipped (non-admin)'
        });

        let systemStatus = null;
        let databaseHealth = null;

        try {
            // 先尝试获取系统状态
            console.log('🔄 请求系统状态...');
            console.log('🔍 API对象检查:', typeof API);
            console.log('🔍 API.get函数检查:', typeof API.get);
            console.log('🔍 当前用户:', currentUser);
            console.log('🔍 认证Token:', localStorage.getItem('authToken') ? '存在' : '不存在');

            systemStatus = await API.get('/system/status');
            console.log('✅ 系统状态响应:', systemStatus);
        } catch (error) {
            console.error('❌ 系统状态请求失败:', error);
            console.error('❌ 错误详情:', {
                message: error.message,
                status: error.status,
                stack: error.stack
            });
            systemStatus = {
                success: false,
                error: error.message,
                system: { serverTime: new Date().toISOString() },
                memory: { totalMemory: 0, freeMemory: 0, usedMemory: 0 },
                database: { connectionStatus: false }
            };
        }

        // 如果是管理员，尝试获取数据库健康信息
        if (currentUser.role === 'ADMIN') {
            try {
                console.log('🔄 请求数据库健康信息...');
                console.log('🔍 管理员权限确认:', currentUser.role);

                databaseHealth = await API.get('/system/database/health');
                console.log('✅ 数据库健康响应:', databaseHealth);
            } catch (error) {
                console.error('❌ 数据库健康请求失败:', error);
                console.error('❌ 数据库健康错误详情:', {
                    message: error.message,
                    status: error.status,
                    stack: error.stack
                });
                databaseHealth = {
                    success: false,
                    error: error.message,
                    connectionStatus: 'ERROR',
                    health: {}
                };
            }
        } else {
            console.log('⚠️ 非管理员用户，跳过数据库健康检查');
        }

        console.log('📊 最终数据:');
        console.log('系统状态:', systemStatus);
        console.log('数据库健康:', databaseHealth);

        renderSystemStatus(systemStatus, databaseHealth);
        console.log('=== 系统管理页面加载完成 ===');

    } catch (error) {
        console.error('=== 系统管理页面加载失败 ===');
        console.error('错误详情:', {
            message: error.message,
            status: error.status,
            stack: error.stack
        });

        if (error.status === 401) {
            console.warn('认证失败，token可能已过期');
            showMessage('认证已过期，请重新登录', 'error');
            setTimeout(() => {
                window.location.href = '/login.html';
            }, 2000);
        } else {
            showMessage(`加载系统信息失败: ${error.message}`, 'error');
            renderSystemError();
        }
    } finally {
        showSystemLoading(false);
    }
}

/**
 * API调用重试机制
 */
async function retryApiCall(apiCall, maxRetries = 2) {
    for (let i = 0; i <= maxRetries; i++) {
        try {
            const result = await apiCall();
            return result;
        } catch (error) {
            console.warn(`API call failed (attempt ${i + 1}/${maxRetries + 1}):`, error);
            if (i === maxRetries) {
                throw error;
            }
            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        }
    }
}

/**
 * 渲染系统状态
 */
function renderSystemStatus(systemData, healthData) {
    console.log('=== 开始渲染系统状态 ===');
    console.log('渲染参数:', {
        systemData: systemData,
        healthData: healthData,
        hasSystemData: !!systemData,
        hasHealthData: !!healthData
    });

    // 提取实际数据（从API响应中获取data字段）
    const actualSystemData = systemData?.data || systemData;
    const actualHealthData = healthData?.data || healthData;

    // 缓存系统数据供后续使用
    if (actualSystemData) {
        window.lastSystemData = actualSystemData;
    }
    if (actualHealthData) {
        window.lastHealthData = actualHealthData;
    }

    console.log('提取后的数据:', {
        actualSystemData: actualSystemData,
        actualHealthData: actualHealthData
    });

    // 更新统计卡片
    updateStatCards(actualSystemData, actualHealthData);

    // 更新系统概览
    updateSystemOverview(actualSystemData, actualHealthData);

    console.log('=== 系统状态渲染完成 ===');
}

/**
 * 更新状态概览
 */
function updateStatCards(systemData, healthData) {
    console.log('--- 开始更新状态概览 ---');
    console.log('状态概览数据:', { systemData, healthData });

    // 更新服务器状态
    const serverStatus = document.getElementById('serverStatus');
    const serverIndicator = document.getElementById('serverIndicator');
    if (serverStatus) {
        if (systemData?.system) {
            serverStatus.textContent = '正常';
            if (serverIndicator) serverIndicator.className = 'status-dot healthy';
        } else {
            serverStatus.textContent = '异常';
            if (serverIndicator) serverIndicator.className = 'status-dot error';
        }
    }

    // 更新数据库状态
    const databaseStatus = document.getElementById('databaseStatus');
    const databaseIndicator = document.getElementById('databaseIndicator');
    if (databaseStatus) {
        const isHealthy = healthData?.connectionStatus === 'HEALTHY' || healthData?.connectionStatus === true;
        databaseStatus.textContent = isHealthy ? '连接正常' : '连接异常';
        if (databaseIndicator) {
            databaseIndicator.className = `status-dot ${isHealthy ? 'healthy' : 'error'}`;
        }
    }

    // 更新内存使用
    const memoryUsage = document.getElementById('memoryUsage');
    const memoryIndicator = document.getElementById('memoryIndicator');
    if (memoryUsage && systemData?.memory) {
        const usedMemory = systemData.memory.usedMemory || 0;
        const totalMemory = systemData.memory.totalMemory || 1;
        const usagePercent = Math.round((usedMemory / totalMemory) * 100);

        memoryUsage.textContent = `${usagePercent}%`;

        if (memoryIndicator) {
            let indicatorClass = 'status-dot ';
            if (usagePercent > 80) indicatorClass += 'error';
            else if (usagePercent > 60) indicatorClass += 'warning';
            else indicatorClass += 'healthy';
            memoryIndicator.className = indicatorClass;
        }
    }

    // 更新运行时间
    const uptime = document.getElementById('uptime');
    const uptimeIndicator = document.getElementById('uptimeIndicator');
    if (uptime) {
        uptime.textContent = '24小时+';
        if (uptimeIndicator) uptimeIndicator.className = 'status-dot healthy';
    }

    console.log('--- 状态概览更新完成 ---');
}

/**
 * 更新系统概览
 */
function updateSystemOverview(systemData, healthData) {
    const container = document.getElementById('systemOverviewContent');
    if (!container) return;

    console.log('更新系统概览:', { systemData, healthData });

    // 如果没有系统数据，尝试从缓存中获取
    if (!systemData) {
        console.log('系统数据缺失，尝试从缓存获取...');
        systemData = window.lastSystemData || null;
    } else {
        // 缓存系统数据
        window.lastSystemData = systemData;
    }

    // 如果没有健康数据，尝试从缓存中获取
    if (!healthData) {
        console.log('健康数据缺失，尝试从缓存获取...');
        healthData = window.lastHealthData || null;
    } else {
        // 缓存健康数据
        window.lastHealthData = healthData;
    }

    if (!systemData) {
        container.innerHTML = '<div class="loading-text">获取系统信息失败</div>';
        return;
    }

    // 计算内存使用率
    const memoryUsagePercent = systemData.memory ?
        Math.round((systemData.memory.usedMemory / systemData.memory.totalMemory) * 100) : 0;

    // 判断数据库连接状态
    const isDatabaseHealthy = healthData?.connectionStatus === 'HEALTHY' || healthData?.connectionStatus === true;

    const html = `
        <!-- 系统基础信息 -->
        <div class="overview-card">
            <div class="overview-card-header">
                <i class="fas fa-server"></i>
                <h4>系统信息</h4>
            </div>
            <div class="overview-card-content">
                <div class="info-row">
                    <span class="info-label">服务器时间：</span>
                    <span class="info-value">${systemData.system?.serverTime ? formatDateTime(systemData.system.serverTime) : '未知'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Java版本：</span>
                    <span class="info-value">${systemData.system?.javaVersion || '未知'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">操作系统：</span>
                    <span class="info-value">${systemData.system?.osName || '未知'} ${systemData.system?.osVersion || ''}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">系统状态：</span>
                    <span class="info-value success">运行正常</span>
                </div>
            </div>
        </div>

        <!-- 内存使用情况 -->
        <div class="overview-card">
            <div class="overview-card-header">
                <i class="fas fa-memory"></i>
                <h4>内存使用</h4>
            </div>
            <div class="overview-card-content">
                <div class="info-row">
                    <span class="info-label">使用率：</span>
                    <span class="info-value ${memoryUsagePercent > 80 ? 'error' : memoryUsagePercent > 60 ? 'warning' : 'success'}">${memoryUsagePercent}%</span>
                </div>
                <div class="info-row">
                    <span class="info-label">已用内存：</span>
                    <span class="info-value">${systemData.memory ? formatBytes(systemData.memory.usedMemory) : '未知'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">总内存：</span>
                    <span class="info-value">${systemData.memory ? formatBytes(systemData.memory.totalMemory) : '未知'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">最大内存：</span>
                    <span class="info-value">${systemData.memory ? formatBytes(systemData.memory.maxMemory) : '未知'}</span>
                </div>
            </div>
        </div>

        <!-- 数据库状态 -->
        <div class="overview-card">
            <div class="overview-card-header">
                <i class="fas fa-database"></i>
                <h4>数据库状态</h4>
            </div>
            <div class="overview-card-content">
                <div class="info-row">
                    <span class="info-label">连接状态：</span>
                    <span class="info-value ${isDatabaseHealthy ? 'success' : 'error'}">${isDatabaseHealthy ? '连接正常' : '连接异常'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">数据完整性：</span>
                    <span class="info-value ${healthData?.dataIntegrity ? 'success' : 'warning'}">${healthData?.dataIntegrity ? '完整' : '需要检查'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">活跃连接：</span>
                    <span class="info-value">${healthData?.activeConnections || '未知'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">检查时间：</span>
                    <span class="info-value">${healthData?.checkTime ? formatDateTime(healthData.checkTime) : '未知'}</span>
                </div>
            </div>
        </div>

        <!-- 数据统计 -->
        <div class="overview-card">
            <div class="overview-card-header">
                <i class="fas fa-chart-bar"></i>
                <h4>数据统计</h4>
            </div>
            <div class="overview-card-content">
                <div class="info-row">
                    <span class="info-label">用户数量：</span>
                    <span class="info-value">${healthData?.health?.usersCount || healthData?.usersCount || '未知'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">项目数量：</span>
                    <span class="info-value">${healthData?.health?.projectsCount || healthData?.projectsCount || '未知'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">任务数量：</span>
                    <span class="info-value">${healthData?.health?.tasksCount || healthData?.tasksCount || '未知'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Bug数量：</span>
                    <span class="info-value">${healthData?.health?.bugsCount || healthData?.bugsCount || '未知'}</span>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}



/**
 * 更新概览卡片（保留原有函数以兼容）
 */
function updateOverviewCards(systemData, healthData) {
    console.log('--- 开始更新概览卡片 ---');
    console.log('更新参数:', {
        systemData: systemData,
        healthData: healthData
    });

    // 更新服务器状态
    const serverStatus = document.getElementById('serverStatus');
    console.log('服务器状态元素:', serverStatus);
    if (serverStatus) {
        if (systemData?.system) {
            console.log('服务器状态: 正常');
            serverStatus.innerHTML = '<i class="fas fa-check-circle"></i> 运行正常';
            const indicator = serverStatus.closest('.overview-card')?.querySelector('.card-indicator');
            if (indicator) indicator.className = 'card-indicator healthy';
        } else {
            console.log('服务器状态: 异常');
            serverStatus.innerHTML = '<i class="fas fa-times-circle"></i> 状态异常';
            const indicator = serverStatus.closest('.overview-card')?.querySelector('.card-indicator');
            if (indicator) indicator.className = 'card-indicator error';
        }
    } else {
        console.warn('未找到服务器状态元素 #serverStatus');
    }

    // 更新数据库状态
    const databaseStatus = document.getElementById('databaseStatus');
    console.log('数据库状态元素:', databaseStatus);
    console.log('数据库健康数据:', healthData);
    if (databaseStatus) {
        if (healthData?.connectionStatus === 'HEALTHY') {
            console.log('数据库状态: 连接正常');
            databaseStatus.innerHTML = '<i class="fas fa-check-circle"></i> 连接正常';
            const indicator = databaseStatus.closest('.overview-card')?.querySelector('.card-indicator');
            if (indicator) indicator.className = 'card-indicator healthy';
        } else {
            console.log('数据库状态: 连接异常，状态:', healthData?.connectionStatus);
            databaseStatus.innerHTML = '<i class="fas fa-times-circle"></i> 连接异常';
            const indicator = databaseStatus.closest('.overview-card')?.querySelector('.card-indicator');
            if (indicator) indicator.className = 'card-indicator error';
        }
    } else {
        console.warn('未找到数据库状态元素 #databaseStatus');
    }

    // 更新内存使用
    const memoryUsage = document.getElementById('memoryUsage');
    const memoryBar = document.getElementById('memoryBar');
    console.log('内存元素:', { memoryUsage, memoryBar });
    console.log('内存数据:', systemData?.memory);

    if (memoryUsage && memoryBar && systemData?.memory) {
        const usedMemory = systemData.memory.usedMemory || 0;
        const totalMemory = systemData.memory.totalMemory || 1;
        const usagePercent = Math.round((usedMemory / totalMemory) * 100);

        console.log('内存使用情况:', {
            usedMemory,
            totalMemory,
            usagePercent
        });

        memoryUsage.textContent = `${usagePercent}%`;
        memoryBar.style.width = `${usagePercent}%`;

        // 根据使用率设置指示器颜色
        const indicator = memoryUsage.closest('.overview-card')?.querySelector('.card-indicator');
        if (indicator) {
            if (usagePercent > 80) {
                indicator.className = 'card-indicator error';
                console.log('内存使用率过高:', usagePercent + '%');
            } else if (usagePercent > 60) {
                indicator.className = 'card-indicator warning';
                console.log('内存使用率警告:', usagePercent + '%');
            } else {
                indicator.className = 'card-indicator healthy';
                console.log('内存使用率正常:', usagePercent + '%');
            }
        }
    } else {
        console.warn('内存更新失败:', {
            hasMemoryUsage: !!memoryUsage,
            hasMemoryBar: !!memoryBar,
            hasMemoryData: !!systemData?.memory
        });
    }

    // 更新运行时间
    const uptime = document.getElementById('uptime');
    console.log('运行时间元素:', uptime);
    if (uptime) {
        uptime.textContent = '24小时+';
        console.log('运行时间已更新');
    } else {
        console.warn('未找到运行时间元素 #uptime');
    }

    console.log('--- 概览卡片更新完成 ---');
}

/**
 * 渲染内存卡片
 */
function renderMemoryCard(memoryData) {
    if (!memoryData) {
        return `
            <div class="info-card">
                <div class="info-header">
                    <i class="fas fa-memory"></i>
                    <span>内存使用</span>
                </div>
                <div class="info-content">
                    <div class="error-state">无法获取内存信息</div>
                </div>
            </div>
        `;
    }

    const usedMemory = memoryData.usedMemory || 0;
    const totalMemory = memoryData.totalMemory || 1;
    const freeMemory = memoryData.freeMemory || 0;
    const usagePercent = Math.round((usedMemory / totalMemory) * 100);

    return `
        <div class="info-card">
            <div class="info-header">
                <i class="fas fa-memory"></i>
                <span>内存使用</span>
            </div>
            <div class="info-content">
                <div class="memory-progress">
                    <div class="progress-bar">
                        <div class="progress-fill ${usagePercent > 80 ? 'danger' : usagePercent > 60 ? 'warning' : 'success'}"
                             style="width: ${usagePercent}%"></div>
                    </div>
                    <div class="progress-text">${usagePercent}% 已使用</div>
                </div>
                <div class="info-item">
                    <span class="label">已使用:</span>
                    <span class="value">${formatBytes(usedMemory)}</span>
                </div>
                <div class="info-item">
                    <span class="label">可用:</span>
                    <span class="value">${formatBytes(freeMemory)}</span>
                </div>
                <div class="info-item">
                    <span class="label">总内存:</span>
                    <span class="value">${formatBytes(totalMemory)}</span>
                </div>
            </div>
        </div>
    `;
}

/**
 * 渲染数据库卡片
 */
function renderDatabaseCard(databaseData, healthData) {
    const connectionStatus = healthData?.connectionStatus === 'HEALTHY';
    const statusText = connectionStatus ? '连接正常' : '连接异常';
    const statusClass = connectionStatus ? 'success' : 'danger';

    return `
        <div class="info-card">
            <div class="info-header">
                <i class="fas fa-database"></i>
                <span>数据库状态</span>
            </div>
            <div class="info-content">
                <div class="status-indicator ${statusClass}">
                    <i class="fas fa-${connectionStatus ? 'check-circle' : 'times-circle'}"></i>
                    <span>${statusText}</span>
                </div>
                ${healthData ? `
                    <div class="info-item">
                        <span class="label">活跃连接:</span>
                        <span class="value">${healthData.activeConnections || 0}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">总连接数:</span>
                        <span class="value">${healthData.totalConnections || 0}</span>
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

/**
 * 渲染内存使用情况
 */
function renderMemorySection(memoryData) {
    const totalMB = Math.round(memoryData.totalMemory / 1024 / 1024);
    const usedMB = Math.round(memoryData.usedMemory / 1024 / 1024);
    const freeMB = Math.round(memoryData.freeMemory / 1024 / 1024);
    const maxMB = Math.round(memoryData.maxMemory / 1024 / 1024);
    const usagePercent = Math.round((usedMB / totalMB) * 100);
    
    return `
        <div class="system-section">
            <h3><i class="fas fa-memory"></i> 内存使用情况</h3>
            <div class="memory-info">
                <div class="memory-stats">
                    <div class="memory-stat">
                        <span class="label">已使用:</span>
                        <span class="value">${usedMB} MB</span>
                    </div>
                    <div class="memory-stat">
                        <span class="label">可用:</span>
                        <span class="value">${freeMB} MB</span>
                    </div>
                    <div class="memory-stat">
                        <span class="label">总计:</span>
                        <span class="value">${totalMB} MB</span>
                    </div>
                    <div class="memory-stat">
                        <span class="label">最大:</span>
                        <span class="value">${maxMB} MB</span>
                    </div>
                </div>
                <div class="memory-bar">
                    <div class="memory-progress" style="width: ${usagePercent}%"></div>
                </div>
                <div class="memory-percentage">${usagePercent}% 已使用</div>
            </div>
        </div>
    `;
}

/**
 * 渲染数据库状态
 */
function renderDatabaseSection(databaseData, healthData) {
    const connectionStatus = databaseData?.connectionStatus;
    const statusClass = connectionStatus ? 'status-up' : 'status-down';
    const statusText = connectionStatus ? '正常' : '异常';
    
    return `
        <div class="system-section">
            <h3><i class="fas fa-database"></i> 数据库状态</h3>
            <div class="database-info">
                <div class="database-status">
                    <span class="status-indicator ${statusClass}"></span>
                    <span class="status-text">连接状态: ${statusText}</span>
                </div>
                
                ${healthData ? `
                    <div class="database-stats">
                        <div class="db-stat">
                            <span class="label">用户数量:</span>
                            <span class="value">${healthData.health.usersCount || 0}</span>
                        </div>
                        <div class="db-stat">
                            <span class="label">项目数量:</span>
                            <span class="value">${healthData.health.projectsCount || 0}</span>
                        </div>
                        <div class="db-stat">
                            <span class="label">任务数量:</span>
                            <span class="value">${healthData.health.tasksCount || 0}</span>
                        </div>
                        <div class="db-stat">
                            <span class="label">评论数量:</span>
                            <span class="value">${healthData.health.commentsCount || 0}</span>
                        </div>
                    </div>
                    
                    <div class="data-integrity">
                        <span class="integrity-label">数据完整性:</span>
                        <span class="integrity-status ${healthData.dataIntegrity ? 'integrity-ok' : 'integrity-error'}">
                            ${healthData.dataIntegrity ? '正常' : '异常'}
                        </span>
                    </div>
                ` : '<p class="text-muted">详细信息需要管理员权限</p>'}
            </div>
        </div>
    `;
}

/**
 * 渲染管理员操作
 */
function renderAdminActions() {
    return `
        <div class="system-section">
            <div class="section-header">
                <h3><i class="fas fa-tools"></i> 管理操作</h3>
                <span class="section-subtitle">仅管理员可执行的系统操作</span>
            </div>
            <div class="admin-actions-grid">
                <div class="action-card primary">
                    <div class="action-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="action-content">
                        <h4>数据库健康检查</h4>
                        <p>检查数据库连接状态和性能指标</p>
                        <button class="btn btn-primary" onclick="refreshDatabaseHealth()">
                            <i class="fas fa-heartbeat"></i> 立即检查
                        </button>
                    </div>
                </div>

                <div class="action-card warning">
                    <div class="action-icon">
                        <i class="fas fa-broom"></i>
                    </div>
                    <div class="action-content">
                        <h4>清理孤立数据</h4>
                        <p>清理数据库中的孤立记录和无效数据</p>
                        <button class="btn btn-warning" onclick="cleanupDatabase()">
                            <i class="fas fa-broom"></i> 开始清理
                        </button>
                    </div>
                </div>

                <div class="action-card danger">
                    <div class="action-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="action-content">
                        <h4>重置数据库</h4>
                        <p>完全重置数据库，保留管理员账号</p>
                        <div class="action-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            此操作不可逆，请谨慎操作
                        </div>
                        <button class="btn btn-danger" onclick="showResetDatabaseModal()">
                            <i class="fas fa-exclamation-triangle"></i> 重置数据库
                        </button>
                    </div>
                </div>

                <div class="action-card info">
                    <div class="action-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="action-content">
                        <h4>系统报告</h4>
                        <p>下载包含系统状态的详细报告</p>
                        <button class="btn btn-info" onclick="downloadSystemReport()">
                            <i class="fas fa-download"></i> 下载报告
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * 显示系统加载状态
 */
function showSystemLoading(show) {
    const container = document.getElementById('systemContent');
    if (!container) return;
    
    if (show) {
        container.innerHTML = `
            <div class="loading-state">
                <i class="fas fa-spinner fa-spin"></i>
                <p>加载系统信息中...</p>
            </div>
        `;
    }
}

/**
 * 渲染系统错误
 */
function renderSystemError() {
    const container = document.getElementById('systemContent');
    if (!container) return;

    container.innerHTML = `
        <div class="error-state">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>加载失败</h3>
            <p>无法加载系统信息，请检查网络连接或稍后重试</p>
            <div class="error-actions">
                <button class="btn btn-primary" onclick="loadSystem()">
                    <i class="fas fa-refresh"></i> 重新加载
                </button>
                <button class="btn btn-secondary" onclick="checkAuthAndReload()">
                    <i class="fas fa-user-check"></i> 检查登录状态
                </button>
            </div>
        </div>
    `;
}

/**
 * 检查认证状态并重新加载
 */
async function checkAuthAndReload() {
    try {
        showMessage('正在检查登录状态...', 'info');
        const response = await API.auth.getCurrentUser();
        if (response && response.success && response.code === 10000 && response.data) {
            currentUser = response.data;
            showMessage('登录状态正常，重新加载页面...', 'success');
            await loadSystem();
        } else {
            // API.js已经处理了认证失败的情况，这里不需要重复处理
            return;
        }
    } catch (error) {
        console.error('Auth check failed:', error);
        // 如果是认证相关错误，API.js已经处理了重定向
        if (error.status === 401 || error.status === 400) {
            return; // API.js会处理重定向
        }
        showMessage('检查登录状态失败，请重新登录', 'error');
        setTimeout(() => {
            window.location.href = '/login.html';
        }, 2000);
    }
}

/**
 * 刷新数据库健康状态
 */
async function refreshDatabaseHealth() {
    try {
        showMessage('正在检查数据库健康状态...', 'info');
        
        const response = await API.get('/system/database/health');
        
        if (response.success) {
            showMessage('数据库健康检查完成', 'success');
            loadSystem(); // 重新加载页面
        } else {
            showMessage(response.message || '健康检查失败', 'error');
        }
    } catch (error) {
        console.error('Database health check failed:', error);
        showMessage('数据库健康检查失败', 'error');
    }
}

/**
 * 清理数据库
 */
async function cleanupDatabase() {
    if (!confirm('确定要清理孤立数据吗？此操作将删除无效的关联记录。')) {
        return;
    }
    
    try {
        showMessage('正在清理数据库...', 'info');
        
        const response = await API.get('/system/database/cleanup');
        
        if (response.success) {
            showMessage('数据库清理完成', 'success');
            loadSystem(); // 重新加载页面
        } else {
            showMessage(response.message || '数据库清理失败', 'error');
        }
    } catch (error) {
        console.error('Database cleanup failed:', error);
        showMessage('数据库清理失败', 'error');
    }
}

/**
 * 下载系统报告
 */
async function downloadSystemReport() {
    try {
        const [systemStatus, databaseHealth, appInfo] = await Promise.all([
            API.get('/system/status'),
            API.get('/system/database/health').catch(() => ({ data: null })),
            API.get('/system/info')
        ]);
        
        const report = {
            generatedAt: new Date().toISOString(),
            system: systemStatus.data,
            database: databaseHealth.data,
            application: appInfo.data
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `system-report-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showMessage('系统报告已下载', 'success');
    } catch (error) {
        console.error('Failed to generate system report:', error);
        showMessage('生成系统报告失败', 'error');
    }
}

// 初始化系统管理页面
document.addEventListener('DOMContentLoaded', function() {
    // 绑定刷新按钮
    const refreshBtn = document.getElementById('refreshSystemBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadSystem);
    }
});

/**
 * 清空数据库
 */
async function clearDatabase() {
    // 显示确认对话框
    const confirmed = await showConfirmDialog(
        '清空数据库确认',
        '此操作将删除所有用户数据（项目、任务、用户等），只保留管理员账号。\n\n确定要继续吗？',
        '清空数据库',
        'btn-danger'
    );

    if (!confirmed) return;

    try {
        showMessage('正在清空数据库...', 'info');

        const response = await API.post('/system/database/clear');

        if (response.success) {
            showMessage('数据库清空成功！只保留管理员账号（admin/123456）', 'success');

            // 刷新系统状态
            setTimeout(() => {
                loadSystem();
            }, 2000);
        } else {
            showMessage(`清空数据库失败: ${response.message}`, 'error');
        }

    } catch (error) {
        console.error('Clear database error:', error);
        showMessage(`清空数据库失败: ${error.message}`, 'error');
    }
}

/**
 * 完全重置数据库
 */
async function resetDatabase() {
    // 显示确认对话框
    const confirmed = await showConfirmDialog(
        '重置数据库确认',
        '此操作将删除所有表并重新创建数据库结构，所有数据将永久丢失！\n\n这是一个不可逆的操作，确定要继续吗？',
        '完全重置',
        'btn-danger'
    );

    if (!confirmed) return;

    try {
        showMessage('正在重置数据库...', 'info');

        const response = await API.post('/system/database/full-reset');

        if (response.success) {
            showMessage('数据库重置成功！已恢复到初始状态，管理员账号：admin/123456', 'success');

            // 刷新系统状态
            setTimeout(() => {
                loadSystem();
            }, 2000);
        } else {
            showMessage(`重置数据库失败: ${response.message}`, 'error');
        }

    } catch (error) {
        console.error('Reset database error:', error);
        showMessage(`重置数据库失败: ${error.message}`, 'error');
    }
}

/**
 * 加载系统日志
 */
async function loadSystemLogs() {
    try {
        const container = document.getElementById('systemLogs');
        if (!container) return;

        container.innerHTML = `
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i> 加载系统日志...
            </div>
        `;

        // 由于没有后端日志接口，显示占位内容
        container.innerHTML = `
            <div class="logs-placeholder">
                <div class="log-entry">
                    <span class="log-time">${new Date().toLocaleString()}</span>
                    <span class="log-level info">INFO</span>
                    <span class="log-message">系统正常运行中</span>
                </div>
                <div class="log-entry">
                    <span class="log-time">${new Date(Date.now() - 60000).toLocaleString()}</span>
                    <span class="log-level info">INFO</span>
                    <span class="log-message">用户登录成功</span>
                </div>
                <div class="log-entry">
                    <span class="log-time">${new Date(Date.now() - 120000).toLocaleString()}</span>
                    <span class="log-level info">INFO</span>
                    <span class="log-message">数据库连接正常</span>
                </div>
                <div class="log-note">
                    <i class="fas fa-info-circle"></i>
                    <span>系统日志功能需要后端支持，当前显示模拟数据</span>
                </div>
            </div>
        `;

    } catch (error) {
        console.error('Failed to load system logs:', error);
        const container = document.getElementById('systemLogs');
        if (container) {
            container.innerHTML = `
                <div class="error-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>加载系统日志失败</p>
                </div>
            `;
        }
    }
}

/**
 * 初始化系统管理页面
 * 新架构中由路由系统调用
 */
window.initSystemPage = async function() {
    console.log('🚀 Initializing system page...');

    try {
        // 确保所有系统管理函数都正确暴露到全局作用域
        console.log('🔍 检查系统管理函数可用性:');
        console.log('showSystemSettings:', typeof window.showSystemSettings);
        console.log('refreshSystemInfo:', typeof window.refreshSystemInfo);
        console.log('checkDatabaseHealth:', typeof window.checkDatabaseHealth);

        // 并行加载系统管理数据和系统日志
        await Promise.all([
            loadSystem(),
            loadSystemLogs()
        ]);

        // 初始化系统配置表单
        initializeSystemConfig();

        // 加载并应用系统设置
        loadCurrentSettings();

        console.log('✅ System page initialized successfully');

        // 再次确认函数可用性
        console.log('🔍 初始化后函数检查:');
        console.log('showSystemSettings:', typeof window.showSystemSettings);

    } catch (error) {
        console.error('❌ Failed to initialize system page:', error);

        // 显示错误状态
        renderSystemError();
    }
};

/**
 * 初始化系统配置
 */
function initializeSystemConfig() {
    // 绑定配置表单事件
    const configForm = document.getElementById('systemConfigForm');
    if (configForm) {
        // 实现配置表单逻辑
        console.log('System config form initialized');
    }
}

/**
 * 刷新所有系统信息
 */
function refreshAllSystemInfo() {
    console.log('🔄 刷新所有系统信息');
    loadSystemData();
}

/**
 * 刷新系统信息
 */
async function refreshSystemInfo() {
    console.log('🔄 刷新系统信息');
    showMessage('正在刷新系统信息...', 'info');
    await loadSystem();
    showMessage('系统信息刷新完成', 'success');
}

/**
 * 检查数据库健康状态
 */
async function checkDatabaseHealth() {
    console.log('=== 开始数据库健康检查 ===');

    try {
        showMessage('正在执行数据库健康检查...', 'info');

        // 并行执行多个检查
        const [healthResponse, statsResponse, duplicatesResponse] = await Promise.all([
            API.get('/system/database/health').catch(err => ({ success: false, error: err.message })),
            API.get('/admin/database/statistics').catch(err => ({ success: false, error: err.message })),
            API.get('/admin/database/duplicates').catch(err => ({ success: false, error: err.message }))
        ]);

        console.log('数据库检查结果:', { healthResponse, statsResponse, duplicatesResponse });

        // 生成详细的检查报告
        const reportData = {
            checkTime: new Date().toLocaleString(),
            healthCheck: healthResponse,
            statistics: statsResponse,
            duplicates: duplicatesResponse,
            summary: generateHealthSummary(healthResponse, statsResponse, duplicatesResponse)
        };

        // 显示检查结果模态框
        showDatabaseHealthReport(reportData);

        // 重新获取系统状态并更新概览
        try {
            const systemStatus = await API.get('/system/status');
            const actualSystemData = systemStatus?.data || systemStatus;
            const actualHealthData = healthResponse?.data || healthResponse;
            updateSystemOverview(actualSystemData, actualHealthData);
        } catch (error) {
            console.error('更新系统概览失败:', error);
            // 如果获取系统状态失败，至少更新数据库信息
            updateSystemOverview(null, healthResponse.data || healthResponse);
        }

        showMessage('数据库健康检查完成', 'success');
        console.log('=== 数据库健康检查完成 ===');

    } catch (error) {
        console.error('=== 数据库健康检查失败 ===');
        console.error('错误详情:', error);
        showMessage(`数据库健康检查失败: ${error.message}`, 'error');
    }
}

/**
 * 生成健康检查摘要
 */
function generateHealthSummary(health, stats, duplicates) {
    const summary = {
        overallHealth: 'healthy',
        issues: [],
        recommendations: []
    };

    // 检查连接状态
    if (!health?.success) {
        summary.overallHealth = 'critical';
        summary.issues.push('数据库连接失败');
        summary.recommendations.push('检查数据库服务状态和连接配置');
    }

    // 检查数据完整性
    if (health?.success && !health.dataIntegrity) {
        summary.overallHealth = 'warning';
        summary.issues.push('数据完整性问题');
        summary.recommendations.push('运行数据清理工具修复孤立数据');
    }

    // 检查重复数据
    if (duplicates?.success && duplicates.data) {
        const totalDuplicates = Object.values(duplicates.data).reduce((sum, items) => sum + (items?.length || 0), 0);
        if (totalDuplicates > 0) {
            summary.overallHealth = summary.overallHealth === 'critical' ? 'critical' : 'warning';
            summary.issues.push(`发现 ${totalDuplicates} 项重复数据`);
            summary.recommendations.push('清理重复数据以提高性能');
        }
    }

    // 检查数据量
    if (stats?.success && stats.data) {
        const totalRecords = Object.values(stats.data).reduce((sum, count) => sum + (count || 0), 0);
        if (totalRecords > 10000) {
            summary.recommendations.push('考虑数据归档以优化性能');
        }
    }

    return summary;
}

/**
 * 显示数据库健康检查报告
 */
function showDatabaseHealthReport(reportData) {
    const modalHtml = `
        <div class="modal-overlay" id="databaseHealthModal">
            <div class="modal-container large">
                <div class="modal-header">
                    <h3><i class="fas fa-heartbeat"></i> 数据库健康检查报告</h3>
                    <button class="modal-close" onclick="closeDatabaseHealthModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="health-report">
                        <!-- 总体状态 -->
                        <div class="health-summary">
                            <div class="summary-card ${reportData.summary.overallHealth}">
                                <div class="summary-icon">
                                    <i class="fas fa-${getHealthIcon(reportData.summary.overallHealth)}"></i>
                                </div>
                                <div class="summary-content">
                                    <h4>总体健康状态</h4>
                                    <p class="health-status">${getHealthStatusText(reportData.summary.overallHealth)}</p>
                                    <p class="check-time">检查时间: ${reportData.checkTime}</p>
                                </div>
                            </div>
                        </div>

                        <!-- 详细信息 -->
                        <div class="health-details">
                            <div class="detail-tabs">
                                <button class="detail-tab active" onclick="switchHealthTab('connection', this)">连接状态</button>
                                <button class="detail-tab" onclick="switchHealthTab('statistics', this)">数据统计</button>
                                <button class="detail-tab" onclick="switchHealthTab('duplicates', this)">重复数据</button>
                                <button class="detail-tab" onclick="switchHealthTab('issues', this)">问题与建议</button>
                            </div>

                            <div class="detail-content">
                                <!-- 连接状态 -->
                                <div class="detail-pane active" id="connectionPane">
                                    ${generateConnectionReport(reportData.healthCheck)}
                                </div>

                                <!-- 数据统计 -->
                                <div class="detail-pane" id="statisticsPane">
                                    ${generateStatisticsReport(reportData.statistics)}
                                </div>

                                <!-- 重复数据 -->
                                <div class="detail-pane" id="duplicatesPane">
                                    ${generateDuplicatesReport(reportData.duplicates)}
                                </div>

                                <!-- 问题与建议 -->
                                <div class="detail-pane" id="issuesPane">
                                    ${generateIssuesReport(reportData.summary)}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="downloadHealthReport('${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-download"></i> 下载报告
                    </button>
                    <button class="btn btn-warning" onclick="cleanupDuplicateData()" ${reportData.duplicates?.success ? '' : 'disabled'}>
                        <i class="fas fa-broom"></i> 清理重复数据
                    </button>
                    <button class="btn btn-primary" onclick="closeDatabaseHealthModal()">关闭</button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

/**
 * 获取健康状态图标
 */
function getHealthIcon(status) {
    switch (status) {
        case 'healthy': return 'check-circle';
        case 'warning': return 'exclamation-triangle';
        case 'critical': return 'times-circle';
        default: return 'question-circle';
    }
}

/**
 * 获取健康状态文本
 */
function getHealthStatusText(status) {
    switch (status) {
        case 'healthy': return '健康';
        case 'warning': return '需要注意';
        case 'critical': return '严重问题';
        default: return '未知';
    }
}

/**
 * 生成连接状态报告
 */
function generateConnectionReport(healthData) {
    if (!healthData?.success) {
        return `
            <div class="report-section error">
                <h5><i class="fas fa-times-circle"></i> 连接失败</h5>
                <p>无法连接到数据库，请检查数据库服务状态。</p>
            </div>
        `;
    }

    return `
        <div class="report-section success">
            <h5><i class="fas fa-check-circle"></i> 连接正常</h5>
            <div class="connection-details">
                <div class="detail-item">
                    <span class="label">连接状态:</span>
                    <span class="value success">正常</span>
                </div>
                <div class="detail-item">
                    <span class="label">数据完整性:</span>
                    <span class="value ${healthData.dataIntegrity ? 'success' : 'warning'}">
                        ${healthData.dataIntegrity ? '完整' : '存在问题'}
                    </span>
                </div>
                <div class="detail-item">
                    <span class="label">检查时间:</span>
                    <span class="value">${healthData.checkTime ? formatDateTime(healthData.checkTime) : '未知'}</span>
                </div>
            </div>
        </div>
    `;
}

/**
 * 生成统计报告
 */
function generateStatisticsReport(statsData) {
    if (!statsData?.success) {
        return `
            <div class="report-section error">
                <h5><i class="fas fa-times-circle"></i> 统计信息获取失败</h5>
                <p>无法获取数据库统计信息。</p>
            </div>
        `;
    }

    const stats = statsData.data; // 修复：使用 data 而不是 statistics
    return `
        <div class="report-section">
            <h5><i class="fas fa-chart-bar"></i> 数据统计</h5>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">${stats.userCount || 0}</div>
                    <div class="stat-label">用户数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${stats.projectCount || 0}</div>
                    <div class="stat-label">项目数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${stats.taskCount || 0}</div>
                    <div class="stat-label">任务数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${stats.commentCount || 0}</div>
                    <div class="stat-label">评论数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${stats.memberCount || 0}</div>
                    <div class="stat-label">成员关系</div>
                </div>
            </div>
        </div>
    `;
}

/**
 * 生成重复数据报告
 */
function generateDuplicatesReport(duplicatesData) {
    if (!duplicatesData?.success) {
        return `
            <div class="report-section error">
                <h5><i class="fas fa-times-circle"></i> 重复数据检查失败</h5>
                <p>无法检查重复数据。</p>
            </div>
        `;
    }

    const duplicates = duplicatesData.data; // 修复：使用 data 而不是 duplicates
    const totalDuplicates = Object.values(duplicates).reduce((sum, items) => sum + (items?.length || 0), 0);

    if (totalDuplicates === 0) {
        return `
            <div class="report-section success">
                <h5><i class="fas fa-check-circle"></i> 无重复数据</h5>
                <p>未发现重复数据，数据库状态良好。</p>
            </div>
        `;
    }

    return `
        <div class="report-section warning">
            <h5><i class="fas fa-exclamation-triangle"></i> 发现重复数据</h5>
            <div class="duplicates-list">
                ${duplicates.projects?.length > 0 ? `
                    <div class="duplicate-category">
                        <h6>重复项目 (${duplicates.projects.length})</h6>
                        <ul>
                            ${duplicates.projects.map(item => `<li>${item.name} (${item.count} 个)</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                ${duplicates.tasks?.length > 0 ? `
                    <div class="duplicate-category">
                        <h6>重复任务 (${duplicates.tasks.length})</h6>
                        <ul>
                            ${duplicates.tasks.map(item => `<li>${item.title} (${item.count} 个)</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                ${duplicates.comments?.length > 0 ? `
                    <div class="duplicate-category">
                        <h6>重复评论 (${duplicates.comments.length})</h6>
                        <ul>
                            ${duplicates.comments.map(item => `<li>${item.content_preview}... (${item.count} 个)</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

/**
 * 生成问题与建议报告
 */
function generateIssuesReport(summary) {
    return `
        <div class="report-section">
            <h5><i class="fas fa-list-ul"></i> 问题与建议</h5>

            ${summary.issues.length > 0 ? `
                <div class="issues-section">
                    <h6><i class="fas fa-exclamation-circle"></i> 发现的问题</h6>
                    <ul class="issues-list">
                        ${summary.issues.map(issue => `<li class="issue-item">${issue}</li>`).join('')}
                    </ul>
                </div>
            ` : `
                <div class="no-issues">
                    <i class="fas fa-check-circle"></i>
                    <p>未发现严重问题</p>
                </div>
            `}

            ${summary.recommendations.length > 0 ? `
                <div class="recommendations-section">
                    <h6><i class="fas fa-lightbulb"></i> 建议</h6>
                    <ul class="recommendations-list">
                        ${summary.recommendations.map(rec => `<li class="recommendation-item">${rec}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}
        </div>
    `;
}

/**
 * 切换健康检查标签页
 */
function switchHealthTab(tabName, element) {
    // 移除所有活动状态
    document.querySelectorAll('.detail-tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.detail-pane').forEach(pane => pane.classList.remove('active'));

    // 激活选中的标签页
    if (element) {
        element.classList.add('active');
    } else {
        // 如果没有传递element，通过事件对象获取
        const clickedTab = event?.target || document.querySelector(`[onclick*="switchHealthTab('${tabName}')"]`);
        if (clickedTab) clickedTab.classList.add('active');
    }

    const targetPane = document.getElementById(tabName + 'Pane');
    if (targetPane) {
        targetPane.classList.add('active');
    }
}

/**
 * 清理重复数据
 */
async function cleanupDuplicateData() {
    try {
        const confirmed = confirm('确定要清理重复数据吗？\n\n这个操作将删除重复的记录，请确保已经备份重要数据。');
        if (!confirmed) return;

        showMessage('正在清理重复数据...', 'info');

        const response = await API.post('/admin/database/cleanup-duplicates');

        if (response.success) {
            showMessage('重复数据清理完成', 'success');
            // 重新检查数据库健康状态
            setTimeout(() => {
                closeDatabaseHealthModal();
                checkDatabaseHealth();
            }, 1000);
        } else {
            showMessage(`清理失败: ${response.message}`, 'error');
        }

    } catch (error) {
        console.error('清理重复数据失败:', error);
        showMessage(`清理重复数据失败: ${error.message}`, 'error');
    }
}

/**
 * 下载健康检查报告
 */
function downloadHealthReport(reportDataStr) {
    try {
        const reportData = JSON.parse(reportDataStr.replace(/&quot;/g, '"'));

        // 生成文本格式的报告
        let reportText = '数据库健康检查报告\n';
        reportText += '='.repeat(50) + '\n\n';
        reportText += `检查时间: ${reportData.checkTime}\n`;
        reportText += `总体状态: ${getHealthStatusText(reportData.summary.overallHealth)}\n\n`;

        // 连接状态
        reportText += '连接状态:\n';
        reportText += `- 数据库连接: ${reportData.healthCheck?.success ? '正常' : '失败'}\n`;
        reportText += `- 数据完整性: ${reportData.healthCheck?.dataIntegrity ? '完整' : '存在问题'}\n\n`;

        // 数据统计
        if (reportData.statistics?.success) {
            reportText += '数据统计:\n';
            const stats = reportData.statistics.data; // 修复：使用 data 而不是 statistics
            reportText += `- 用户数量: ${stats.userCount || 0}\n`;
            reportText += `- 项目数量: ${stats.projectCount || 0}\n`;
            reportText += `- 任务数量: ${stats.taskCount || 0}\n`;
            reportText += `- 评论数量: ${stats.commentCount || 0}\n`;
            reportText += `- 成员关系: ${stats.memberCount || 0}\n\n`;
        }

        // 问题和建议
        if (reportData.summary.issues.length > 0) {
            reportText += '发现的问题:\n';
            reportData.summary.issues.forEach(issue => {
                reportText += `- ${issue}\n`;
            });
            reportText += '\n';
        }

        if (reportData.summary.recommendations.length > 0) {
            reportText += '建议:\n';
            reportData.summary.recommendations.forEach(rec => {
                reportText += `- ${rec}\n`;
            });
        }

        // 下载文件
        const blob = new Blob([reportText], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `database-health-report-${new Date().toISOString().split('T')[0]}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showMessage('健康检查报告已下载', 'success');

    } catch (error) {
        console.error('下载报告失败:', error);
        showMessage('下载报告失败', 'error');
    }
}

/**
 * 关闭数据库健康检查模态框
 */
function closeDatabaseHealthModal() {
    const modal = document.getElementById('databaseHealthModal');
    if (modal) {
        modal.remove();
    }
}

/**
 * 清理系统缓存
 */
async function clearSystemCache() {
    console.log('=== 开始清理系统缓存 ===');

    try {
        // 显示确认对话框
        const confirmed = confirm('确定要清理系统缓存吗？\n\n这将清理以下内容：\n• 浏览器本地存储\n• 会话存储\n• 应用程序缓存\n\n清理后页面将自动刷新。');

        if (!confirmed) {
            console.log('用户取消了缓存清理操作');
            return;
        }

        showMessage('正在清理系统缓存...', 'info');

        // 清理浏览器缓存
        console.log('清理浏览器本地存储...');

        // 保存当前用户token，避免清理后需要重新登录
        const currentToken = localStorage.getItem('authToken');
        const currentUserData = localStorage.getItem('currentUser');

        // 清理localStorage（除了认证信息）
        const keysToKeep = ['authToken', 'currentUser'];
        const allKeys = Object.keys(localStorage);
        allKeys.forEach(key => {
            if (!keysToKeep.includes(key)) {
                localStorage.removeItem(key);
                console.log(`已清理localStorage项: ${key}`);
            }
        });

        // 清理sessionStorage
        sessionStorage.clear();
        console.log('已清理sessionStorage');

        // 清理应用程序缓存（如果支持）
        if ('caches' in window) {
            const cacheNames = await caches.keys();
            await Promise.all(
                cacheNames.map(cacheName => {
                    console.log(`清理缓存: ${cacheName}`);
                    return caches.delete(cacheName);
                })
            );
        }

        // 清理内存中的临时数据
        if (window.tempData) {
            window.tempData = {};
            console.log('已清理临时数据');
        }

        showMessage('系统缓存清理完成，页面即将刷新...', 'success');
        console.log('=== 系统缓存清理完成 ===');

        // 延迟刷新页面
        setTimeout(() => {
            window.location.reload();
        }, 1500);

    } catch (error) {
        console.error('=== 清理系统缓存失败 ===');
        console.error('错误详情:', {
            message: error.message,
            stack: error.stack
        });
        showMessage(`清理系统缓存失败: ${error.message}`, 'error');
    }
}

/**
 * 显示系统日志
 */
function showSystemLogs() {
    console.log('=== 显示系统日志 ===');

    // 创建模态框HTML
    const modalHtml = `
        <div class="modal-overlay" id="systemLogsModal">
            <div class="modal-dialog modal-lg">
                <div class="modal-header">
                    <h3><i class="fas fa-file-alt"></i> 系统日志</h3>
                    <button class="modal-close" onclick="closeSystemLogsModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="logs-controls">
                        <div class="controls-left">
                            <select id="logLevel" onchange="filterLogs()">
                                <option value="all">所有类型</option>
                                <option value="error">ERROR</option>
                                <option value="warn">WARN</option>
                                <option value="info">INFO</option>
                                <option value="debug">DEBUG</option>
                            </select>
                            <input type="text" id="logSearch" placeholder="搜索日志..." onkeyup="filterLogs()">
                        </div>
                        <div class="controls-right">
                            <button class="btn btn-outline btn-sm" onclick="refreshLogs()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                            <button class="btn btn-outline btn-sm" onclick="clearLogsDisplay()">
                                <i class="fas fa-trash"></i> 清空显示
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="downloadLogs()">
                                <i class="fas fa-download"></i> 下载
                            </button>
                        </div>
                    </div>
                    <div class="logs-container" id="logsContainer">
                        <div class="loading-logs">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>加载日志中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    setTimeout(() => {
        const modal = document.getElementById('systemLogsModal');
        if (modal) {
            modal.classList.add('show');
        }
    }, 10);

    // 加载日志数据
    loadSystemLogsData();
}

/**
 * 加载系统日志数据
 */
async function loadSystemLogsData() {
    const container = document.getElementById('logsContainer');

    try {
        // 模拟系统日志数据（实际项目中应该从后端API获取）
        const mockLogs = generateMockLogs();

        let logsHtml = '';
        mockLogs.forEach(log => {
            logsHtml += `
                <div class="log-entry ${log.level}" data-level="${log.level}">
                    <div class="log-content">
                        <div class="log-header">
                            <div class="log-main-info">
                                <span class="log-time">${log.timestamp}</span>
                                <span class="log-level ${log.level}">${log.level.toUpperCase()}</span>
                                <span class="log-message">${log.message}</span>
                            </div>
                            ${log.details ? `
                                <button class="log-details-toggle" onclick="toggleLogDetails(this)">
                                    <i class="fas fa-chevron-down"></i> 查看详细信息
                                </button>
                            ` : ''}
                        </div>
                        ${log.details ? `
                            <div class="log-details" style="display: none;">
                                <pre>${formatLogDetails(log.details)}</pre>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        });

        container.innerHTML = logsHtml;

    } catch (error) {
        console.error('加载系统日志失败:', error);
        container.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <p>加载系统日志失败: ${error.message}</p>
            </div>
        `;
    }
}

/**
 * 生成模拟日志数据
 */
function generateMockLogs() {
    const logData = [
        { level: 'info', message: '系统启动完成', details: null },
        { level: 'info', message: '用户登录成功', details: 'User: admin, IP: 127.0.0.1, Session: abc123' },
        { level: 'info', message: '数据库连接建立', details: 'Connection Pool: HikariCP, Active: 5/20' },
        { level: 'info', message: '缓存清理完成', details: 'Cleared 1,234 cache entries, Memory freed: 45MB' },
        { level: 'info', message: '定时任务执行', details: 'Task: DataBackup, Duration: 2.3s, Status: SUCCESS' },
        { level: 'debug', message: '内存使用率检查', details: 'Heap: 512MB/1024MB (50%), Non-Heap: 128MB/256MB (50%)' },
        { level: 'debug', message: '文件上传处理', details: 'File: document.pdf, Size: 2.5MB, Path: /uploads/2024/07/' },
        { level: 'info', message: '权限验证通过', details: 'User: admin, Resource: /api/users, Permission: READ' },
        { level: 'info', message: '数据备份完成', details: 'Backup file: backup_20240714.sql, Size: 15.2MB' },
        { level: 'warn', message: '磁盘空间不足警告', details: 'Disk usage: 85% (/var/log), Available: 2.1GB, Threshold: 80%' },
        { level: 'warn', message: '用户会话即将过期', details: 'User: john_doe, Session: xyz789, Expires in: 5 minutes' },
        { level: 'error', message: '数据库查询超时', details: `SQLException: Query timeout after 30 seconds
Query: SELECT * FROM projects WHERE status = 'IN_PROGRESS' AND created_date > '2024-01-01'
Connection: jdbc:mysql://localhost:3306/project_management
Stack trace:
  at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1201)
  at com.projectmanagement.service.ProjectService.findActiveProjects(ProjectService.java:45)
  at com.projectmanagement.controller.ProjectController.getProjects(ProjectController.java:78)` },
        { level: 'error', message: '文件读取错误', details: `IOException: Permission denied
File: /var/log/application.log
Cause: java.io.FileNotFoundException: /var/log/application.log (Permission denied)
Stack trace:
  at java.io.FileInputStream.open0(Native Method)
  at java.io.FileInputStream.open(FileInputStream.java:195)
  at com.projectmanagement.util.LogReader.readFile(LogReader.java:23)` },
        { level: 'error', message: '网络连接异常', details: `ConnectException: Connection refused
Host: api.external-service.com:443
Timeout: 5000ms
Retry attempts: 3/3
Last error: java.net.ConnectException: Connection timed out
Stack trace:
  at java.net.PlainSocketImpl.socketConnect(Native Method)
  at com.projectmanagement.service.ExternalApiService.callApi(ExternalApiService.java:67)` }
    ];

    const logs = [];
    const now = new Date();

    // 生成50条日志记录
    for (let i = 0; i < 50; i++) {
        const timestamp = new Date(now.getTime() - i * 60000 * Math.random() * 10);
        const logEntry = logData[Math.floor(Math.random() * logData.length)];

        logs.push({
            timestamp: timestamp.toLocaleString(),
            level: logEntry.level,
            message: logEntry.message,
            details: logEntry.details
        });
    }

    return logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
}

/**
 * 过滤日志
 */
function filterLogs() {
    const level = document.getElementById('logLevel').value;
    const search = document.getElementById('logSearch').value.toLowerCase();
    const logEntries = document.querySelectorAll('.log-entry');

    logEntries.forEach(entry => {
        const entryLevel = entry.dataset.level;
        const entryText = entry.textContent.toLowerCase();

        const levelMatch = level === 'all' || entryLevel === level;
        const searchMatch = search === '' || entryText.includes(search);

        entry.style.display = levelMatch && searchMatch ? 'block' : 'none';
    });
}

/**
 * 格式化日志详细信息，确保长行不会超出容器
 */
function formatLogDetails(details) {
    if (!details) return '';

    // 转义HTML字符
    const escaped = details
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');

    // 处理长行，确保不会水平溢出
    const lines = escaped.split('\n');
    const formattedLines = lines.map(line => {
        if (line.length > 100) {
            // 对于非常长的行，在合适的位置强制换行
            let result = '';
            let currentLine = line;

            while (currentLine.length > 100) {
                // 寻找最佳断点：空格、点、逗号、分号、冒号
                let breakPoint = -1;
                for (let i = 90; i < Math.min(100, currentLine.length); i++) {
                    if (/[.,;:\s]/.test(currentLine[i])) {
                        breakPoint = i + 1;
                    }
                }

                if (breakPoint === -1) {
                    // 如果找不到合适的断点，强制在100字符处断开
                    breakPoint = 100;
                }

                result += currentLine.substring(0, breakPoint) + '\n';
                currentLine = '    ' + currentLine.substring(breakPoint); // 添加缩进
            }
            result += currentLine;
            return result;
        }
        return line;
    });

    return formattedLines.join('\n');
}

/**
 * 切换日志详细信息显示
 */
function toggleLogDetails(button) {
    // 找到按钮所在的log-entry容器
    const logEntry = button.closest('.log-entry');
    // 在log-entry中查找log-details容器
    const detailsContainer = logEntry.querySelector('.log-details');

    if (!detailsContainer) {
        console.error('找不到详细信息容器');
        return;
    }

    if (detailsContainer.style.display === 'none' || detailsContainer.style.display === '') {
        // 显示详细信息
        detailsContainer.style.display = 'block';
        button.innerHTML = '<i class="fas fa-chevron-up"></i> 隐藏详细信息';
    } else {
        // 隐藏详细信息
        detailsContainer.style.display = 'none';
        button.innerHTML = '<i class="fas fa-chevron-down"></i> 查看详细信息';
    }
}

/**
 * 刷新日志
 */
function refreshLogs() {
    loadSystemLogsData();
    showMessage('日志已刷新', 'success');
}

/**
 * 清空日志显示
 */
function clearLogsDisplay() {
    const container = document.getElementById('logsContainer');
    container.innerHTML = '<div class="empty-logs">日志显示已清空</div>';
}

/**
 * 下载日志
 */
function downloadLogs() {
    const logEntries = document.querySelectorAll('.log-entry:not([style*="display: none"])');
    let logText = '系统日志导出\n';
    logText += '导出时间: ' + new Date().toLocaleString() + '\n';
    logText += '='.repeat(50) + '\n\n';

    logEntries.forEach(entry => {
        const time = entry.querySelector('.log-time').textContent;
        const level = entry.querySelector('.log-level').textContent;
        const message = entry.querySelector('.log-message').textContent;
        const details = entry.querySelector('.log-details');

        logText += `[${time}] ${level}: ${message}\n`;
        if (details) {
            logText += `详情: ${details.textContent}\n`;
        }
        logText += '\n';
    });

    const blob = new Blob([logText], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system-logs-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showMessage('日志文件已下载', 'success');
}

/**
 * 关闭系统日志模态框
 */
function closeSystemLogsModal() {
    const modal = document.getElementById('systemLogsModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300); // 等待动画完成
    }
}

/**
 * 显示系统设置 - 完全重写版本
 */
window.showSystemSettings = function showSystemSettings() {
    console.log('🚀 启动系统设置功能');

    // 移除已存在的模态框
    const existingModal = document.getElementById('systemSettingsModal');
    if (existingModal) {
        existingModal.remove();
        console.log('🗑️ 移除了已存在的模态框');
    }

    // 创建模态框容器
    const modalOverlay = document.createElement('div');
    modalOverlay.id = 'systemSettingsModal';
    modalOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;

    // 创建模态框主体
    const modalDialog = document.createElement('div');
    modalDialog.style.cssText = `
        background: white;
        border-radius: 8px;
        width: 90%;
        max-width: 900px;
        max-height: 90vh;
        display: flex;
        flex-direction: column;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    `;

    // 创建模态框头部
    const modalHeader = document.createElement('div');
    modalHeader.style.cssText = `
        padding: 20px;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f8f9fa;
    `;
    modalHeader.innerHTML = `
        <h3 style="margin: 0; color: #212529; font-size: 18px;">
            <i class="fas fa-cog" style="margin-right: 10px; color: #007bff;"></i>系统设置
        </h3>
        <button onclick="closeSystemSettingsModal()" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #6c757d; padding: 5px;">
            <i class="fas fa-times"></i>
        </button>
    `;

    // 创建标签导航
    const tabNav = document.createElement('div');
    tabNav.style.cssText = `
        display: flex;
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 0 20px;
    `;

    const tabs = [
        { id: 'general', name: '常规设置', icon: 'fas fa-sliders-h' },
        { id: 'notification', name: '通知设置', icon: 'fas fa-bell' },
        { id: 'security', name: '安全设置', icon: 'fas fa-shield-alt' },
        { id: 'performance', name: '性能设置', icon: 'fas fa-tachometer-alt' },
        { id: 'maintenance', name: '维护设置', icon: 'fas fa-tools' }
    ];

    tabs.forEach((tab, index) => {
        const tabBtn = document.createElement('button');
        tabBtn.id = `tab-${tab.id}`;
        tabBtn.style.cssText = `
            padding: 15px 20px;
            background: none;
            border: none;
            border-bottom: 3px solid transparent;
            color: #6c757d;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
        `;
        tabBtn.innerHTML = `<i class="${tab.icon}"></i> ${tab.name}`;
        tabBtn.onclick = () => switchToTab(tab.id);

        if (index === 0) {
            tabBtn.style.color = '#007bff';
            tabBtn.style.borderBottomColor = '#007bff';
        }

        tabNav.appendChild(tabBtn);
    });

    // 创建内容区域
    const modalBody = document.createElement('div');
    modalBody.style.cssText = `
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background: white;
    `;

    // 创建标签页内容容器
    const tabContent = document.createElement('div');
    tabContent.id = 'tabContent';
    tabContent.style.cssText = `
        min-height: 400px;
    `;

    modalBody.appendChild(tabContent);
    // 创建模态框底部
    const modalFooter = document.createElement('div');
    modalFooter.style.cssText = `
        padding: 20px;
        border-top: 1px solid #dee2e6;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        background: #f8f9fa;
    `;
    modalFooter.innerHTML = `
        <button onclick="resetSystemSettings()" style="padding: 8px 16px; border: 1px solid #6c757d; background: white; color: #6c757d; border-radius: 4px; cursor: pointer;">
            重置
        </button>
        <button onclick="closeSystemSettingsModal()" style="padding: 8px 16px; border: 1px solid #6c757d; background: white; color: #6c757d; border-radius: 4px; cursor: pointer;">
            取消
        </button>
        <button onclick="saveSystemSettings()" style="padding: 8px 16px; border: 1px solid #007bff; background: #007bff; color: white; border-radius: 4px; cursor: pointer;">
            保存设置
        </button>
    `;

    // 组装模态框
    modalDialog.appendChild(modalHeader);
    modalDialog.appendChild(tabNav);
    modalDialog.appendChild(modalBody);
    modalDialog.appendChild(modalFooter);
    modalOverlay.appendChild(modalDialog);

    // 添加到页面
    document.body.appendChild(modalOverlay);

    // 显示模态框
    setTimeout(() => {
        modalOverlay.style.opacity = '1';
        console.log('✅ 模态框已显示');

        // 加载当前设置到表单
        loadCurrentSettings();
    }, 10);

    // 默认显示第一个标签页
    switchToTab('general');

    console.log('🎉 系统设置模态框创建完成');
}

/**
 * 切换到指定标签页
 */
window.switchToTab = function switchToTab(tabId) {
    console.log(`🔄 切换到标签页: ${tabId}`);

    // 重置所有标签按钮样式
    const allTabs = document.querySelectorAll('#systemSettingsModal [id^="tab-"]');
    allTabs.forEach(tab => {
        tab.style.color = '#6c757d';
        tab.style.borderBottomColor = 'transparent';
    });

    // 激活当前标签按钮
    const currentTab = document.getElementById(`tab-${tabId}`);
    if (currentTab) {
        currentTab.style.color = '#007bff';
        currentTab.style.borderBottomColor = '#007bff';
    }

    // 显示对应的内容
    const tabContent = document.getElementById('tabContent');
    if (tabContent) {
        tabContent.innerHTML = getTabContent(tabId);
        console.log(`✅ 已加载 ${tabId} 标签页内容`);

        // 加载当前设置到新生成的表单控件
        setTimeout(() => {
            loadCurrentSettingsToForm();
        }, 50);
    }
}

/**
 * 获取标签页内容
 */
function getTabContent(tabId) {
    const contentMap = {
        general: getGeneralSettings(),
        notification: getNotificationSettings(),
        security: getSecuritySettings(),
        performance: getPerformanceSettings(),
        maintenance: getMaintenanceSettings()
    };

    return contentMap[tabId] || '<div>内容加载失败</div>';
}

/**
 * 生成常规设置内容
 */
function getGeneralSettings() {
    return `
        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: 600; color: #212529; padding-bottom: 8px; border-bottom: 1px solid #dee2e6;">
                <i class="fas fa-palette" style="margin-right: 8px; color: #007bff;"></i>界面设置
            </h4>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="darkMode" style="margin-right: 10px; transform: scale(1.2);" /> 深色模式
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">切换到深色主题界面</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="compactMode" style="margin-right: 10px; transform: scale(1.2);" /> 紧凑布局模式
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">减少界面元素间距，显示更多内容</div>
            </div>
        </div>

        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: 600; color: #212529; padding-bottom: 8px; border-bottom: 1px solid #dee2e6;">
                <i class="fas fa-clock" style="margin-right: 8px; color: #007bff;"></i>时间与日期
            </h4>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">时区</label>
                <select id="timezone" style="width: 100%; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px; box-sizing: border-box;">
                    <option value="Asia/Shanghai" selected>中国标准时间 (GMT+8)</option>
                    <option value="UTC">协调世界时 (UTC)</option>
                    <option value="America/New_York">美国东部时间 (UTC-5)</option>
                    <option value="Europe/London">英国时间 (UTC+0)</option>
                </select>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">选择您所在的时区</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">日期格式</label>
                <select id="dateFormat" style="width: 100%; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px; box-sizing: border-box;">
                    <option value="YYYY-MM-DD" selected>2024-12-25</option>
                    <option value="MM/DD/YYYY">12/25/2024</option>
                    <option value="DD/MM/YYYY">25/12/2024</option>
                    <option value="DD-MM-YYYY">25-12-2024</option>
                </select>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">选择日期显示格式</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">时间格式</label>
                <select id="timeFormat" style="width: 100%; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px; box-sizing: border-box;">
                    <option value="24" selected>24小时制 (14:30)</option>
                    <option value="12">12小时制 (2:30 PM)</option>
                </select>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">选择时间显示格式</div>
            </div>
        </div>

        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: 600; color: #212529; padding-bottom: 8px; border-bottom: 1px solid #dee2e6;">
                <i class="fas fa-list" style="margin-right: 8px; color: #007bff;"></i>数据显示
            </h4>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">默认排序方式</label>
                <select id="defaultSort" style="width: 100%; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px; box-sizing: border-box;">
                    <option value="created_desc" selected>按创建时间（最新优先）</option>
                    <option value="created_asc">按创建时间（最旧优先）</option>
                    <option value="priority_desc">按优先级（高到低）</option>
                    <option value="due_date_asc">按截止日期（近到远）</option>
                    <option value="name_asc">按名称（A-Z）</option>
                </select>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">列表页面的默认排序方式</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="showAvatars" checked style="margin-right: 10px; transform: scale(1.2);" /> 显示用户头像
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">在列表中显示用户头像</div>
            </div>
        </div>
    `;
}

/**
 * 生成通知设置内容
 */
function getNotificationSettings() {
    return `
        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: 600; color: #212529; padding-bottom: 8px; border-bottom: 1px solid #dee2e6;">
                <i class="fas fa-bell" style="margin-right: 8px; color: #007bff;"></i>通知开关
            </h4>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="enableTaskNotifications" checked style="margin-right: 10px; transform: scale(1.2);" /> 任务通知
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">任务分配、状态更新、截止日期提醒</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="enableProjectNotifications" checked style="margin-right: 10px; transform: scale(1.2);" /> 项目通知
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">项目创建、状态变更、成员变动</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="enableBugNotifications" checked style="margin-right: 10px; transform: scale(1.2);" /> Bug通知
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">Bug报告、状态更新、修复完成</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="enableSystemNotifications" style="margin-right: 10px; transform: scale(1.2);" /> 系统通知
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">系统维护、更新公告、安全提醒</div>
            </div>
        </div>

        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: 600; color: #212529; padding-bottom: 8px; border-bottom: 1px solid #dee2e6;">
                <i class="fas fa-volume-up" style="margin-right: 8px; color: #007bff;"></i>通知方式
            </h4>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="enableBrowserNotifications" checked style="margin-right: 10px; transform: scale(1.2);" /> 浏览器推送
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">在浏览器中显示桌面通知</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="enableSoundNotifications" style="margin-right: 10px; transform: scale(1.2);" /> 声音提醒
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">播放提示音</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">通知频率</label>
                <select id="notificationFrequency" style="width: 100%; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px; box-sizing: border-box;">
                    <option value="immediate" selected>立即通知</option>
                    <option value="hourly">每小时汇总</option>
                    <option value="daily">每日汇总</option>
                    <option value="weekly">每周汇总</option>
                </select>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">选择接收通知的频率</div>
            </div>
        </div>

        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: 600; color: #212529; padding-bottom: 8px; border-bottom: 1px solid #dee2e6;">
                <i class="fas fa-clock" style="margin-right: 8px; color: #007bff;"></i>工作时间
            </h4>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="enableWorkingHours" style="margin-right: 10px; transform: scale(1.2);" /> 仅在工作时间通知
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">只在设定的工作时间内发送通知</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">工作时间</label>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <select id="workStartTime" style="flex: 1; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px;">
                        <option value="08:00">08:00</option>
                        <option value="09:00" selected>09:00</option>
                        <option value="10:00">10:00</option>
                    </select>
                    <span style="color: #6c757d;">至</span>
                    <select id="workEndTime" style="flex: 1; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px;">
                        <option value="17:00">17:00</option>
                        <option value="18:00" selected>18:00</option>
                        <option value="19:00">19:00</option>
                        <option value="20:00">20:00</option>
                    </select>
                </div>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">设置您的工作时间范围</div>
            </div>
        </div>
    `;
}

/**
 * 生成安全设置内容
 */
function getSecuritySettings() {
    return `
        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: 600; color: #212529; padding-bottom: 8px; border-bottom: 1px solid #dee2e6;">
                <i class="fas fa-shield-alt" style="margin-right: 8px; color: #007bff;"></i>认证设置
            </h4>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">JWT Token过期时间（小时）</label>
                <select id="tokenExpiry" style="width: 100%; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px; box-sizing: border-box;">
                    <option value="1">1小时</option>
                    <option value="6">6小时</option>
                    <option value="12">12小时</option>
                    <option value="24" selected>24小时</option>
                    <option value="72">3天</option>
                    <option value="168">7天</option>
                </select>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">设置用户登录Token的有效期</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="autoLogout" checked style="margin-right: 10px; transform: scale(1.2);" /> 自动登出过期用户
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">Token过期时自动跳转到登录页面</div>
            </div>
        </div>

        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: 600; color: #212529; padding-bottom: 8px; border-bottom: 1px solid #dee2e6;">
                <i class="fas fa-key" style="margin-right: 8px; color: #007bff;"></i>密码策略
            </h4>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">最小密码长度</label>
                <select id="minPasswordLength" style="width: 100%; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px; box-sizing: border-box;">
                    <option value="4">4位</option>
                    <option value="6" selected>6位</option>
                    <option value="8">8位</option>
                    <option value="12">12位</option>
                </select>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">新用户注册时的最小密码长度要求</div>
            </div>
        </div>
    `;
}

/**
 * 生成性能设置内容
 */
function getPerformanceSettings() {
    return `
        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: 600; color: #212529; padding-bottom: 8px; border-bottom: 1px solid #dee2e6;">
                <i class="fas fa-rocket" style="margin-right: 8px; color: #007bff;"></i>性能优化
            </h4>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="enableCache" checked style="margin-right: 10px; transform: scale(1.2);" /> 启用浏览器缓存
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">缓存静态资源以提高页面加载速度</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="enableLazyLoad" checked style="margin-right: 10px; transform: scale(1.2);" /> 启用懒加载
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">延迟加载图片和非关键内容</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">页面大小限制</label>
                <select id="pageSize" style="width: 100%; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px; box-sizing: border-box;">
                    <option value="10">10条/页</option>
                    <option value="20" selected>20条/页</option>
                    <option value="50">50条/页</option>
                    <option value="100">100条/页</option>
                </select>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">列表页面每页显示的数据条数</div>
            </div>
        </div>

        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: 600; color: #212529; padding-bottom: 8px; border-bottom: 1px solid #dee2e6;">
                <i class="fas fa-paint-brush" style="margin-right: 8px; color: #007bff;"></i>个性化设置
            </h4>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">字体大小</label>
                <select id="fontSize" style="width: 100%; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px; box-sizing: border-box;">
                    <option value="small">小号字体</option>
                    <option value="medium" selected>标准字体</option>
                    <option value="large">大号字体</option>
                    <option value="xlarge">超大字体</option>
                </select>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">调整界面字体大小</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">表格行高</label>
                <select id="tableRowHeight" style="width: 100%; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px; box-sizing: border-box;">
                    <option value="compact">紧凑</option>
                    <option value="normal" selected>标准</option>
                    <option value="comfortable">舒适</option>
                </select>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">调整表格行的高度</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">侧边栏宽度</label>
                <select id="sidebarWidth" style="width: 100%; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px; box-sizing: border-box;">
                    <option value="narrow">窄边栏 (200px)</option>
                    <option value="normal" selected>标准边栏 (250px)</option>
                    <option value="wide">宽边栏 (300px)</option>
                </select>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">调整左侧导航栏宽度</div>
            </div>
        </div>
    `;
}

/**
 * 生成维护设置内容
 */
function getMaintenanceSettings() {
    return `
        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: 600; color: #212529; padding-bottom: 8px; border-bottom: 1px solid #dee2e6;">
                <i class="fas fa-broom" style="margin-right: 8px; color: #007bff;"></i>数据清理
            </h4>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="autoCleanup" checked style="margin-right: 10px; transform: scale(1.2);" /> 自动清理过期数据
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">定期清理过期的日志和临时文件</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">清理频率</label>
                <select id="cleanupFrequency" style="width: 100%; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px; box-sizing: border-box;">
                    <option value="daily">每日</option>
                    <option value="weekly" selected>每周</option>
                    <option value="monthly">每月</option>
                </select>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">自动清理的执行频率</div>
            </div>
        </div>

        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: 600; color: #212529; padding-bottom: 8px; border-bottom: 1px solid #dee2e6;">
                <i class="fas fa-bell" style="margin-right: 8px; color: #007bff;"></i>通知设置
            </h4>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; font-weight: 500; color: #212529; font-size: 14px; cursor: pointer;">
                    <input type="checkbox" id="enableNotifications" checked style="margin-right: 10px; transform: scale(1.2);" /> 启用系统通知
                </label>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">启用任务分配、状态更新等通知</div>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #212529; font-size: 14px;">通知保留天数</label>
                <select id="notificationRetention" style="width: 100%; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: white; color: #212529; font-size: 14px; box-sizing: border-box;">
                    <option value="7">7天</option>
                    <option value="30" selected>30天</option>
                    <option value="90">90天</option>
                    <option value="365">1年</option>
                </select>
                <div style="margin-top: 5px; font-size: 12px; color: #6c757d;">通知消息的保留时间</div>
            </div>
        </div>
    `;
}

/**
 * 关闭系统设置模态框
 */
window.closeSystemSettingsModal = function closeSystemSettingsModal() {
    console.log('🔒 关闭系统设置模态框');
    const modal = document.getElementById('systemSettingsModal');
    if (modal) {
        modal.style.opacity = '0';
        setTimeout(() => {
            modal.remove();
            console.log('✅ 模态框已移除');
        }, 300);
    }
}

/**
 * 重置系统设置
 */
window.resetSystemSettings = function resetSystemSettings() {
    console.log('🔄 重置系统设置');
    if (confirm('确定要重置所有设置为默认值吗？')) {
        // 重新加载当前标签页内容
        const activeTab = document.querySelector('#systemSettingsModal [id^="tab-"][style*="color: rgb(0, 123, 255)"]');
        if (activeTab) {
            const tabId = activeTab.id.replace('tab-', '');
            switchToTab(tabId);
        }
        console.log('✅ 设置已重置');
    }
}



/**
 * 兼容旧版本的标签页切换函数
 */
window.switchSettingsTab = function switchSettingsTab(tabName, element) {
    console.log('🔄 兼容模式 - 切换标签页:', tabName);
    switchToTab(tabName);
}

/**
 * 加载当前设置（包含应用到界面）
 */
function loadCurrentSettings() {
    try {
        // 从localStorage加载保存的设置
        const savedSettings = JSON.parse(localStorage.getItem('systemSettings') || '{}');

        // 应用保存的设置到表单
        loadCurrentSettingsToForm();

        // 应用设置到界面
        applySettings(savedSettings);

        console.log('已加载系统设置:', savedSettings);
    } catch (error) {
        console.error('加载系统设置失败:', error);
    }
}

/**
 * 仅加载设置到表单控件（不应用到界面）
 */
async function loadCurrentSettingsToForm() {
    try {
        // 首先尝试从后端加载设置
        let savedSettings = {};

        try {
            const response = await api.get('/api/user/settings');
            if (response.success && response.settings) {
                savedSettings = response.settings;
                console.log('从后端加载设置成功:', savedSettings);
            }
        } catch (error) {
            console.warn('从后端加载设置失败，使用本地存储:', error.message);
            // 如果后端加载失败，从localStorage加载
            savedSettings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
        }

        // 应用保存的设置到表单
        Object.keys(savedSettings).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = savedSettings[key];
                } else {
                    element.value = savedSettings[key];
                }
            }
        });

        console.log('已加载设置到表单:', savedSettings);
    } catch (error) {
        console.error('加载设置到表单失败:', error);
    }
}

/**
 * 保存系统设置
 */
window.saveSystemSettings = async function saveSystemSettings() {
    try {
        showMessage('正在保存系统设置...', 'info');

        // 收集所有设置
        const settings = {};
        const formElements = document.querySelectorAll('#systemSettingsModal input, #systemSettingsModal select, #systemSettingsModal textarea');

        formElements.forEach(element => {
            if (element.id) {
                if (element.type === 'checkbox') {
                    settings[element.id] = element.checked;
                } else if (element.type === 'number') {
                    settings[element.id] = parseInt(element.value) || 0;
                } else {
                    settings[element.id] = element.value;
                }
            }
        });

        // 发送到后端保存
        const response = await api.post('/api/user/settings', settings);

        if (response.success) {
            // 同时保存到localStorage作为备份
            localStorage.setItem('systemSettings', JSON.stringify(settings));

            // 应用设置
            applySettings(settings);

            showMessage('系统设置已保存', 'success');
            console.log('已保存系统设置:', settings);

            // 关闭模态框
            setTimeout(() => {
                closeSystemSettingsModal();
            }, 1000);
        } else {
            throw new Error(response.message || '保存设置失败');
        }

    } catch (error) {
        console.error('保存系统设置失败:', error);
        showMessage(`保存系统设置失败: ${error.message}`, 'error');
    }
}

/**
 * 应用设置
 */
function applySettings(settings) {
    // 应用深色模式
    if (settings.darkMode) {
        document.body.classList.add('dark-mode');
    } else {
        document.body.classList.remove('dark-mode');
    }

    // 应用紧凑模式
    if (settings.compactMode) {
        document.body.classList.add('compact-mode');
    } else {
        document.body.classList.remove('compact-mode');
    }

    // 应用字体大小设置
    document.body.classList.remove('font-small', 'font-large', 'font-xlarge');
    if (settings.fontSize && settings.fontSize !== 'medium') {
        document.body.classList.add(`font-${settings.fontSize}`);
    }

    // 应用表格行高设置
    document.body.classList.remove('table-compact', 'table-comfortable');
    if (settings.tableRowHeight && settings.tableRowHeight !== 'normal') {
        document.body.classList.add(`table-${settings.tableRowHeight}`);
    }

    // 应用侧边栏宽度设置
    document.body.classList.remove('sidebar-narrow', 'sidebar-wide');
    if (settings.sidebarWidth && settings.sidebarWidth !== 'normal') {
        document.body.classList.add(`sidebar-${settings.sidebarWidth}`);
    }

    console.log('✅ 设置已应用到界面');
}

/**
 * 重置设置
 */
function resetSettings() {
    const confirmed = confirm('确定要重置所有设置为默认值吗？');
    if (confirmed) {
        localStorage.removeItem('systemSettings');
        loadCurrentSettings();
        showMessage('设置已重置为默认值', 'success');
    }
}

/**
 * 关闭系统设置模态框
 */
function closeSystemSettingsModal() {
    const modal = document.getElementById('systemSettingsModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300); // 等待动画完成
    }
}

/**
 * 显示数据库重置确认模态框
 */
async function showResetDatabaseModal() {
    // 权限预检查
    const hasPermission = await checkPermissionBeforeAction('SYSTEM_DATABASE');
    if (!hasPermission) {
        return;
    }
    console.log('=== 显示数据库重置确认模态框 ===');
    console.log('用户点击重置数据库按钮');

    const modal = createModal('重置数据库', `
        <div class="reset-database-modal">
            <div class="warning-section">
                <div class="warning-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="warning-content">
                    <h4>危险操作警告</h4>
                    <p>您即将执行数据库重置操作，此操作将：</p>
                    <ul>
                        <li>删除所有项目、任务、Bug等业务数据</li>
                        <li>删除除管理员外的所有用户账号</li>
                        <li>重置系统到初始状态</li>
                        <li><strong>此操作不可逆转</strong></li>
                    </ul>
                </div>
            </div>

            <div class="confirmation-section">
                <div class="form-group">
                    <label for="confirmText">请输入 "RESET" 确认操作：</label>
                    <input type="text" id="confirmText" class="form-control" placeholder="输入 RESET 确认">
                </div>
                <div class="checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="confirmCheckbox">
                        <span class="checkmark"></span>
                        我已了解此操作的风险并确认执行
                    </label>
                </div>
            </div>
        </div>
    `, [
        {
            text: '取消',
            className: 'btn-outline',
            onClick: () => {
                console.log('用户取消数据库重置操作');
                closeModal();
            }
        },
        {
            text: '确认重置',
            className: 'btn-danger',
            onClick: () => {
                console.log('用户确认数据库重置操作');
                confirmResetDatabase();
            }
        }
    ]);

    console.log('数据库重置确认模态框已显示');
}

/**
 * 确认重置数据库
 */
async function confirmResetDatabase() {
    console.log('=== 开始数据库重置确认流程 ===');

    const confirmText = document.getElementById('confirmText');
    const confirmCheckbox = document.getElementById('confirmCheckbox');

    console.log('表单元素检查:', {
        confirmText: !!confirmText,
        confirmCheckbox: !!confirmCheckbox,
        confirmTextValue: confirmText?.value,
        checkboxChecked: confirmCheckbox?.checked
    });

    if (!confirmText || !confirmCheckbox) {
        console.error('表单元素未找到');
        showMessage('表单元素未找到', 'error');
        return;
    }

    if (confirmText.value !== 'RESET') {
        console.warn('确认文本不正确:', confirmText.value);
        showMessage('请正确输入 "RESET" 确认操作', 'error');
        return;
    }

    if (!confirmCheckbox.checked) {
        console.warn('确认复选框未勾选');
        showMessage('请勾选确认复选框', 'error');
        return;
    }

    console.log('所有确认条件满足，开始执行数据库重置...');

    try {
        closeModal();
        showMessage('正在重置数据库，请稍候...', 'info');

        console.log('发送数据库重置请求: POST /system/reset');
        console.log('请求头信息:', {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
        });

        const response = await API.post('/system/reset');

        console.log('数据库重置响应:', response);

        if (response.success) {
            console.log('数据库重置成功');
            showMessage('数据库重置成功！系统将在3秒后刷新页面', 'success');
            setTimeout(() => {
                console.log('刷新页面...');
                window.location.reload();
            }, 3000);
        } else {
            console.error('数据库重置失败:', response.message);
            showMessage(`数据库重置失败: ${response.message}`, 'error');
        }

    } catch (error) {
        console.error('=== 数据库重置异常 ===');
        console.error('错误详情:', {
            message: error.message,
            status: error.status,
            response: error.response,
            stack: error.stack
        });

        if (error.status === 401) {
            console.warn('认证失败，token可能已过期');
            showMessage('认证已过期，请重新登录', 'error');
            setTimeout(() => {
                window.location.href = '/login.html';
            }, 2000);
        } else {
            showMessage(`数据库重置失败: ${error.message}`, 'error');
        }
    }
}



/**
 * 刷新数据库健康状态
 */
async function refreshDatabaseHealth() {
    console.log('=== 开始数据库健康检查 ===');

    try {
        showMessage('正在检查数据库健康状态...', 'info');

        console.log('发送数据库健康检查请求: GET /system/database/health');
        const healthData = await API.get('/system/database/health');
        console.log('数据库健康检查响应:', healthData);

        showMessage('数据库健康检查完成', 'success');

        console.log('重新获取系统状态...');
        const systemData = await API.get('/system/status');
        console.log('系统状态响应:', systemData);

        console.log('重新渲染系统状态...');
        renderSystemStatus(systemData, healthData);

        console.log('=== 数据库健康检查完成 ===');

    } catch (error) {
        console.error('=== 数据库健康检查失败 ===');
        console.error('错误详情:', {
            message: error.message,
            status: error.status,
            response: error.response
        });

        if (error.status === 401) {
            console.warn('认证失败，token可能已过期');
            // API.js已经处理了认证失败的情况，这里不需要重复处理
            return;
        } else {
            showMessage(`数据库健康检查失败: ${error.message}`, 'error');
        }
    }
}

/**
 * 清理数据库
 */
async function cleanupDatabase() {
    console.log('=== 开始数据库清理 ===');

    try {
        showMessage('正在清理数据库...', 'info');

        console.log('发送数据库清理请求: GET /system/database/cleanup');
        const response = await API.get('/system/database/cleanup');
        console.log('数据库清理响应:', response);

        showMessage('数据库清理完成', 'success');
        console.log('=== 数据库清理完成 ===');

    } catch (error) {
        console.error('=== 数据库清理失败 ===');
        console.error('错误详情:', {
            message: error.message,
            status: error.status,
            response: error.response
        });

        if (error.status === 401) {
            console.warn('认证失败，token可能已过期');
            showMessage('认证已过期，请重新登录', 'error');
            setTimeout(() => {
                window.location.href = '/login.html';
            }, 2000);
        } else {
            showMessage(`数据库清理失败: ${error.message}`, 'error');
        }
    }
}

/**
 * 下载系统报告
 */
async function downloadSystemReport() {
    console.log('=== 开始下载系统报告 ===');

    try {
        showMessage('正在生成系统报告...', 'info');

        // 并行获取系统信息
        const [systemStatus, databaseHealth, appInfo] = await Promise.all([
            API.get('/system/status').catch(err => ({ success: false, error: err.message })),
            API.get('/system/database/health').catch(err => ({ success: false, error: err.message })),
            API.get('/system/info').catch(err => ({ success: false, error: err.message }))
        ]);

        console.log('收集到的系统信息:', { systemStatus, databaseHealth, appInfo });

        // 生成报告数据
        const reportData = {
            reportInfo: {
                title: '系统状态报告',
                generatedAt: new Date().toISOString(),
                generatedBy: currentUser?.username || 'Unknown',
                version: '1.0.0'
            },
            systemStatus: systemStatus,
            databaseHealth: databaseHealth,
            applicationInfo: appInfo,
            summary: {
                systemHealthy: systemStatus?.success && databaseHealth?.success,
                totalChecks: 3,
                passedChecks: [systemStatus?.success, databaseHealth?.success, appInfo?.success].filter(Boolean).length,
                reportSize: 'Generated dynamically'
            }
        };

        // 创建并下载文件
        const reportJson = JSON.stringify(reportData, null, 2);
        const blob = new Blob([reportJson], { type: 'application/json;charset=utf-8' });
        const url = URL.createObjectURL(blob);

        const currentDate = new Date().toISOString().split('T')[0];
        const fileName = `system-report-${currentDate}.json`;

        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = fileName;
        downloadLink.style.display = 'none';

        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        URL.revokeObjectURL(url);

        showMessage(`系统报告已下载: ${fileName}`, 'success');
        console.log('=== 系统报告下载完成 ===');

    } catch (error) {
        console.error('=== 下载系统报告失败 ===');
        console.error('错误详情:', {
            message: error.message,
            status: error.status,
            stack: error.stack
        });
        showMessage(`下载系统报告失败: ${error.message}`, 'error');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌟 系统管理页面DOM加载完成');
    console.log('当前页面URL:', window.location.href);
    console.log('当前用户信息:', currentUser);
    console.log('本地存储的Token:', localStorage.getItem('authToken') ? '存在' : '不存在');
    console.log('浏览器信息:', {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform
    });

    loadSystem();
});

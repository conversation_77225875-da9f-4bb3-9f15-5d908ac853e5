/* ===== 系统管理页面样式 ===== */
.system-management-page {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    padding: 0;
}

/* 系统状态概览 */
.system-management-page .status-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.system-management-page .status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition-base);
}

.system-management-page .status-item:hover {
    background: var(--bg-secondary);
}

.system-management-page .status-icon {
    color: var(--primary-color);
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.system-management-page .status-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    min-width: 60px;
}

.system-management-page .status-value {
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
    text-align: right;
}

.system-management-page .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.system-management-page .status-dot.healthy {
    background: var(--success-color);
}

.system-management-page .status-dot.warning {
    background: var(--warning-color);
}

.system-management-page .status-dot.error {
    background: var(--danger-color);
}

/* 系统内容区域 */
.system-management-page .system-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* 系统概览区域 */
.system-management-page .system-overview-section {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.system-management-page .system-overview-section h3 {
    margin: 0;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-light);
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.system-management-page .overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    grid-auto-rows: auto;
    align-items: start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
}

.system-management-page .overview-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    transition: var(--transition-base);
    height: fit-content;
    align-self: start;
}

.system-management-page .overview-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.system-management-page .overview-card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--border-light);
}

.system-management-page .overview-card-header i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.system-management-page .overview-card-header h4 {
    margin: 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.system-management-page .overview-card-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    min-height: auto;
    flex-shrink: 0;
}

.system-management-page .overview-card .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-size: var(--font-size-sm);
    min-height: auto;
    flex-shrink: 0;
}

.system-management-page .overview-card .info-row:last-child {
    border-bottom: none;
}

.system-management-page .overview-card .info-label {
    font-weight: 500;
    color: var(--text-secondary);
    flex-shrink: 0;
}

.system-management-page .overview-card .info-value {
    font-weight: 600;
    color: var(--text-primary);
    text-align: right;
}

.system-management-page .overview-card .info-value.success {
    color: var(--success-color);
}

.system-management-page .overview-card .info-value.error {
    color: var(--danger-color);
}

.system-management-page .overview-card .info-value.warning {
    color: var(--warning-color);
}

.system-management-page .info-section h3 i {
    color: var(--primary-color);
    font-size: 0.9rem;
}

.system-management-page .info-content {
    padding: var(--spacing-md);
}

.system-management-page .loading-text {
    color: var(--text-secondary);
    font-style: italic;
}

/* 操作区域 */
.system-management-page .actions-section {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.system-management-page .actions-section h3 {
    margin: 0;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-light);
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.system-management-page .actions-section h3 i {
    color: var(--primary-color);
    font-size: 0.9rem;
}

.system-management-page .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
}

.system-management-page .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    transition: var(--transition-base);
    cursor: pointer;
    text-align: center;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

.system-management-page .action-btn:hover {
    background: var(--bg-hover);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.system-management-page .action-btn.danger {
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.system-management-page .action-btn.danger:hover {
    background: rgba(220, 53, 69, 0.1);
    border-color: var(--danger-color);
}

.system-management-page .action-btn i {
    font-size: 1.2rem;
}

.system-management-page .action-btn span {
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .system-management-page .status-overview {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
    }

    .system-management-page .status-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-xs);
    }

    .system-management-page .status-label {
        min-width: auto;
    }

    .system-management-page .info-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .system-management-page .actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: var(--spacing-xs);
        padding: var(--spacing-md);
    }

    .system-management-page .action-btn {
        padding: var(--spacing-sm);
        font-size: var(--font-size-xs);
    }

    .system-management-page .action-btn i {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .system-management-page {
        gap: var(--spacing-md);
    }

    .system-management-page .status-overview {
        grid-template-columns: 1fr;
        padding: var(--spacing-sm);
    }

    .system-management-page .status-item {
        flex-direction: row;
        text-align: left;
        padding: var(--spacing-xs);
    }

    .system-management-page .info-content {
        padding: var(--spacing-md);
    }

    .system-management-page .actions-grid {
        grid-template-columns: repeat(2, 1fr);
        padding: var(--spacing-sm);
    }

    .system-management-page .action-btn {
        padding: var(--spacing-xs);
        gap: 2px;
    }

    .system-management-page .action-btn span {
        font-size: 0.7rem;
    }

    /* 模态框响应式 */
    .modal-container {
        width: 95%;
        max-height: 95vh;
    }

    .modal-container.large {
        width: 98%;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-md);
    }

    .logs-controls {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .controls-left {
        width: 100%;
    }

    .controls-right {
        width: 100%;
        justify-content: flex-start;
    }

    .logs-controls input {
        min-width: auto;
    }

    .log-entry {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }

    .log-time,
    .log-level {
        font-size: 0.7rem;
    }

    .tab-nav {
        flex-wrap: wrap;
    }

    .tab-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.75rem;
    }

    .settings-tabs {
        height: auto;
        max-height: 400px;
    }
}

/* 数据库健康检查报告样式 */
.health-report {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.health-summary {
    margin-bottom: var(--spacing-lg);
}

.summary-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    border: 2px solid;
}

.summary-card.healthy {
    background: rgba(40, 167, 69, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.summary-card.warning {
    background: rgba(255, 193, 7, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.summary-card.critical {
    background: rgba(220, 53, 69, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.summary-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.summary-content h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.health-status {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin: 0;
}

.check-time {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    margin: var(--spacing-xs) 0 0 0;
}

.health-details {
    flex: 1;
}

.detail-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-light);
    margin-bottom: var(--spacing-lg);
}

.detail-tab {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-base);
    font-size: var(--font-size-sm);
}

.detail-tab:hover {
    color: var(--text-primary);
    background: var(--bg-hover);
}

.detail-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--bg-hover);
}

.detail-content {
    min-height: 300px;
}

.detail-pane {
    display: none;
}

.detail-pane.active {
    display: block;
}

.report-section {
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
}

.report-section.success {
    background: rgba(40, 167, 69, 0.1);
    border-left: 4px solid var(--success-color);
}

.report-section.warning {
    background: rgba(255, 193, 7, 0.1);
    border-left: 4px solid var(--warning-color);
}

.report-section.error {
    background: rgba(220, 53, 69, 0.1);
    border-left: 4px solid var(--danger-color);
}

.report-section h5 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.connection-details,
.stats-grid {
    display: grid;
    gap: var(--spacing-sm);
}

.stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
}

.detail-item .label {
    font-weight: 500;
    color: var(--text-secondary);
}

.detail-item .value {
    font-weight: 600;
}

.detail-item .value.success {
    color: var(--success-color);
}

.detail-item .value.warning {
    color: var(--warning-color);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.duplicates-list {
    margin-top: var(--spacing-md);
}

.duplicate-category {
    margin-bottom: var(--spacing-md);
}

.duplicate-category h6 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.duplicate-category ul {
    margin: 0;
    padding-left: var(--spacing-lg);
    list-style-type: disc;
}

.duplicate-category li {
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.no-issues {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--success-color);
}

.no-issues i {
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
}

.issues-section,
.recommendations-section {
    margin-bottom: var(--spacing-lg);
}

.issues-section h6,
.recommendations-section h6 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.issues-list,
.recommendations-list {
    margin: 0;
    padding-left: var(--spacing-lg);
    list-style-type: none;
}

.issue-item,
.recommendation-item {
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
    position: relative;
}

.issue-item::before {
    content: '⚠️';
    position: absolute;
    left: -var(--spacing-lg);
}

.recommendation-item::before {
    content: '💡';
    position: absolute;
    left: -var(--spacing-lg);
}

/* 信息列表样式 */
.system-management-page .info-list {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.system-management-page .info-row {
    display: flex;
    align-items: center;
    padding: 2px 0;
    font-size: var(--font-size-sm);
    line-height: 1.3;
}

.system-management-page .info-label {
    font-weight: 500;
    color: var(--text-secondary);
    min-width: 80px;
    flex-shrink: 0;
}

.system-management-page .info-value {
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
    word-break: break-all;
}

.system-management-page .info-value.success {
    color: var(--success-color);
}

.system-management-page .info-value.error {
    color: var(--danger-color);
}

.system-management-page .info-value.warning {
    color: var(--warning-color);
}

/* 系统管理模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(2px);
}

.modal-container {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-container.large {
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
}

.modal-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.modal-header h3 i {
    color: var(--primary-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
    transition: var(--transition-base);
}

.modal-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-xl);
    flex: 1;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-light);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

/* 系统日志样式 */
.logs-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    gap: var(--spacing-md);
}

.controls-left {
    display: flex;
    gap: var(--spacing-sm);
    flex: 1;
}

.controls-right {
    display: flex;
    gap: var(--spacing-sm);
}

.logs-controls select,
.logs-controls input {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.logs-controls input {
    min-width: 200px;
}

.logs-container {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    height: 400px;
    overflow-y: auto;
    padding: var(--spacing-sm);
}

.loading-logs {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-secondary);
    gap: var(--spacing-sm);
}

.logs-list {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.log-entry {
    display: grid;
    grid-template-columns: 150px 60px 1fr;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

.log-entry:hover {
    background: var(--bg-hover);
}

.log-time {
    color: var(--text-secondary);
    font-family: monospace;
}

.log-level {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 3px;
    text-align: center;
}

.log-level.info {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

.log-level.warn {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.log-level.error {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.log-level.debug {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.log-message {
    color: var(--text-primary);
}

.log-details {
    grid-column: 1 / -1;
    color: var(--text-secondary);
    font-style: italic;
    margin-top: var(--spacing-xs);
    padding-left: var(--spacing-lg);
}

.empty-logs {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-secondary);
    font-style: italic;
}

/* 系统设置样式 */
.settings-tabs {
    display: flex;
    flex-direction: column;
    min-height: 500px;
    max-height: 70vh;
}

.tab-nav {
    display: flex;
    border-bottom: 1px solid var(--border-light);
    margin-bottom: var(--spacing-lg);
}

.tab-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-base);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.tab-btn:hover {
    color: var(--text-primary);
    background: var(--bg-hover);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--bg-hover);
}

.tab-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-sm);
    background: var(--bg-primary);
}

/* 通用标签页样式 */
.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 系统设置模态框标签页样式 */
#systemSettingsModal .tab-pane {
    display: none !important;
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    min-height: 300px;
}

#systemSettingsModal .tab-pane.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 系统设置模态框特定样式 */
#systemSettingsModal .settings-section {
    margin-bottom: var(--spacing-xl);
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-light);
}

#systemSettingsModal .settings-section h4 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--border-light);
}

#systemSettingsModal .setting-item {
    margin-bottom: var(--spacing-md);
    display: block; /* 确保不使用flex布局 */
    background: transparent;
    padding: var(--spacing-xs) 0;
}

#systemSettingsModal .setting-item label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

#systemSettingsModal .setting-item input,
#systemSettingsModal .setting-item select,
#systemSettingsModal .setting-item textarea {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    box-sizing: border-box;
}

#systemSettingsModal .setting-item input[type="checkbox"] {
    width: auto;
    margin-right: var(--spacing-xs);
}

#systemSettingsModal .setting-item label input[type="checkbox"] {
    display: inline;
    margin-right: var(--spacing-xs);
    vertical-align: middle;
}

#systemSettingsModal .setting-item input:focus,
#systemSettingsModal .setting-item select:focus,
#systemSettingsModal .setting-item textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

/* 系统管理页面头部 */
.system-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.system-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.system-title {
    position: relative;
    z-index: 1;
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.system-title i {
    color: var(--primary-color);
    font-size: 1.2em;
}

.system-subtitle {
    position: relative;
    z-index: 1;
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

/* 系统概览 */
.system-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.overview-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
}

.overview-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.overview-card h3 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.overview-card h3 i {
    color: var(--primary-color);
}

.overview-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
}

.stat-row .label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.stat-row .value {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.stat-row .value.success {
    color: var(--success-color);
}

.stat-row .value.warning {
    color: var(--warning-color);
}

.stat-row .value.error {
    color: var(--danger-color);
}

/* 系统信息网格 */
.system-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

/* 管理员操作网格 */
.admin-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.admin-action-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: var(--transition-fast);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.admin-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-color);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.admin-action-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.admin-action-card:hover::before {
    transform: scaleX(1);
}

.admin-action-card.danger {
    border-color: var(--danger-color);
}

.admin-action-card.danger::before {
    background: var(--danger-color);
}

.admin-action-card.danger:hover {
    border-color: var(--danger-color);
    background: rgba(220, 53, 69, 0.05);
}

.action-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    transition: var(--transition-fast);
}

.admin-action-card.danger .action-icon {
    color: var(--danger-color);
}

.admin-action-card:hover .action-icon {
    background: var(--primary-color);
    color: var(--text-white);
    transform: scale(1.1);
}

.admin-action-card.danger:hover .action-icon {
    background: var(--danger-color);
}

.action-title {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.action-desc {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

/* 系统管理标签页 */
.system-tabs {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.system-tabs .tab-nav {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    overflow-x: auto;
}

.system-tabs .tab-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.system-tabs .tab-btn:hover {
    color: var(--text-primary);
    background: rgba(0, 123, 255, 0.1);
}

.system-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--bg-primary);
}

.system-tabs .tab-content {
    padding: var(--spacing-xl);
    min-height: 400px;
}

/* 监控标签页 */
.monitor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.monitor-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    transition: var(--transition-fast);
}

.monitor-card:hover {
    box-shadow: var(--shadow);
    transform: translateY(-2px);
}

.monitor-card h4 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.monitor-card h4 i {
    color: var(--primary-color);
}

.monitor-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.monitor-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-light);
}

.monitor-stat:last-child {
    border-bottom: none;
}

.monitor-stat .label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.monitor-stat .value {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

/* 数据库管理标签页 */
.database-section {
    margin-bottom: var(--spacing-xl);
}

.database-section h4 {
    margin: 0 0 var(--spacing-lg) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-light);
}

.database-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.database-action {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    transition: var(--transition-fast);
}

.database-action:hover {
    background: var(--bg-hover);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.database-action.danger {
    border-color: var(--danger-color);
}

.database-action.danger:hover {
    border-color: var(--danger-color);
    background: rgba(220, 53, 69, 0.1);
}

.database-action i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.database-action.danger i {
    color: var(--danger-color);
}

.database-action h5 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.database-action p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

/* 系统配置标签页 */
.config-section {
    margin-bottom: var(--spacing-xl);
}

.config-section h4 {
    margin: 0 0 var(--spacing-lg) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-light);
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.config-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
}

.config-card h5 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.config-item {
    margin-bottom: var(--spacing-md);
}

.config-item:last-child {
    margin-bottom: 0;
}

.config-item label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.config-item input,
.config-item select,
.config-item textarea {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.config-item input:focus,
.config-item select:focus,
.config-item textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 系统日志标签页 */
.logs-section {
    display: flex;
    flex-direction: column;
    height: 500px;
}

.logs-header {
    margin-bottom: var(--spacing-lg);
}

.logs-header h4 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.logs-body {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.logs-content {
    flex: 1;
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* 系统管理页面响应式设计 - 完整版 */
@media (max-width: 1200px) {
    .system-overview {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
    }

    .system-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }

    .admin-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: var(--spacing-sm);
    }

    .monitor-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }

    .database-actions {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: var(--spacing-sm);
    }

    .config-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }
}

@media (max-width: 992px) {
    .system-header {
        padding: var(--spacing-lg);
    }

    .system-title {
        font-size: var(--font-size-xl);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .system-overview {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    .overview-card {
        padding: var(--spacing-md);
    }

    .system-info-grid {
        grid-template-columns: 1fr;
    }

    .admin-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    }

    .admin-action-card {
        padding: var(--spacing-md);
    }

    .action-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }

    .system-tabs .tab-nav {
        flex-wrap: wrap;
    }

    .system-tabs .tab-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-xs);
    }

    .system-tabs .tab-content {
        padding: var(--spacing-lg);
    }

    .monitor-grid {
        grid-template-columns: 1fr;
    }

    .database-actions {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .config-grid {
        grid-template-columns: 1fr;
    }

    .logs-controls {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .controls-left,
    .controls-right {
        width: 100%;
        justify-content: flex-start;
    }

    .logs-controls input {
        min-width: auto;
        width: 100%;
    }
}

/* 系统管理页面增强样式 */
.system-status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.system-status-indicator.online {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.system-status-indicator.offline {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.system-status-indicator.maintenance {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.system-status-indicator::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 系统性能指标 */
.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.metric-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.metric-trend {
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.metric-trend.up {
    color: var(--success-color);
}

.metric-trend.down {
    color: var(--danger-color);
}

.metric-trend.stable {
    color: var(--text-secondary);
}

/* 系统日志增强样式 */
.log-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.logs-container {
    max-height: 400px;
    overflow-y: auto;
    background: var(--bg-secondary);
}

.logs-list {
    padding: var(--spacing-lg);
}

.log-entry {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    font-family: var(--font-family-monospace);
    font-size: var(--font-size-xs);
    line-height: 1.4;
}

.log-entry.error {
    background: rgba(220, 53, 69, 0.1);
    border-left: 3px solid var(--danger-color);
}

.log-entry.warn {
    background: rgba(255, 193, 7, 0.1);
    border-left: 3px solid var(--warning-color);
}

.log-entry.info {
    background: rgba(23, 162, 184, 0.1);
    border-left: 3px solid var(--info-color);
}

.log-entry.debug {
    background: rgba(108, 117, 125, 0.1);
    border-left: 3px solid var(--secondary-color);
}

.log-time {
    color: var(--text-muted);
    font-weight: 500;
    min-width: 80px;
}

.log-level {
    font-weight: 600;
    text-transform: uppercase;
    min-width: 50px;
}

.log-level.error { color: var(--danger-color); }
.log-level.warn { color: var(--warning-color); }
.log-level.info { color: var(--info-color); }
.log-level.debug { color: var(--secondary-color); }

.log-message {
    flex: 1;
    color: var(--text-primary);
}

/* 加载和错误状态 */
.loading-state, .error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
    text-align: center;
    color: var(--text-secondary);
    min-height: 120px;
    max-height: 200px;
}

.loading-state .spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-sm);
}

.loading-state p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-state i {
    font-size: 3rem;
    color: var(--danger-color);
    margin-bottom: var(--spacing-md);
}

.error-state h3 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
}

.error-state p {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-secondary);
}

/* 系统操作卡片增强 */
.action-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: var(--transition-fast);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    transform: scaleY(0);
    transform-origin: bottom;
    transition: transform 0.3s ease;
}

.action-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.action-card:hover::before {
    transform: scaleY(1);
}

.action-card.danger::before {
    background: var(--danger-color);
}

.action-card.danger:hover {
    border-color: var(--danger-color);
    background: rgba(220, 53, 69, 0.05);
}

.action-icon {
    width: 60px;
    height: 60px;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.action-card:nth-child(1) .action-icon {
    background: linear-gradient(135deg, var(--danger-color), #ff6b6b);
    color: var(--text-white);
}

.action-card:nth-child(2) .action-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: var(--text-white);
}

.action-card:nth-child(3) .action-icon {
    background: linear-gradient(135deg, var(--warning-color), #ffa726);
    color: var(--text-white);
}

.action-content {
    flex: 1;
}

.action-content h4 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: 600;
}

.action-content p {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

/* 系统配置表单 */
.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    padding: var(--spacing-xl);
}

.config-group {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
}

.config-group h4 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: 600;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.config-group h4::before {
    content: '⚙️';
    font-size: var(--font-size-sm);
}

.form-actions {
    padding: var(--spacing-xl);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-light);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* 系统管理页面最终响应式设计 */
@media (max-width: 480px) {
    .system-management-page {
        padding: var(--spacing-sm);
    }

    .system-header {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }

    .system-title {
        font-size: var(--font-size-lg);
    }

    .system-overview {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .overview-card {
        padding: var(--spacing-sm);
    }

    .overview-card h3 {
        font-size: var(--font-size-sm);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .admin-actions-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }

    .admin-action-card {
        padding: var(--spacing-sm);
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .action-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }

    .action-title {
        font-size: var(--font-size-sm);
    }

    .action-desc {
        font-size: var(--font-size-xs);
    }

    .system-tabs .tab-nav {
        flex-direction: column;
    }

    .system-tabs .tab-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        border-bottom: none;
        border-right: 3px solid transparent;
        text-align: left;
    }

    .system-tabs .tab-btn.active {
        border-right-color: var(--primary-color);
        border-bottom-color: transparent;
    }

    .system-tabs .tab-content {
        padding: var(--spacing-md);
    }

    .performance-metrics {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xs);
    }

    .metric-card {
        padding: var(--spacing-sm);
    }

    .metric-value {
        font-size: 1.5rem;
    }

    .metric-label {
        font-size: var(--font-size-xs);
    }

    .log-entry {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs);
    }

    .log-time, .log-level {
        min-width: auto;
    }

    .config-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }

    .config-group {
        padding: var(--spacing-sm);
    }

    .form-actions {
        padding: var(--spacing-md);
        flex-direction: column;
    }

    .modal-container {
        width: 95%;
        max-height: 95vh;
    }

    .modal-container.large {
        width: 98%;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-sm);
    }

    .section-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
        padding: var(--spacing-md);
    }

    .log-actions {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .log-actions select {
        flex: 1;
        min-width: 120px;
    }

    .form-actions .btn {
        width: 100%;
    }

    .action-card {
        flex-direction: column;
        text-align: center;
    }

    .action-icon {
        align-self: center;
    }
}
